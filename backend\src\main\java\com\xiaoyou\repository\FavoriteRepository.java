package com.xiaoyou.repository;

import com.xiaoyou.entity.Favorite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface FavoriteRepository extends JpaRepository<Favorite, Long> {
    
    /**
     * 根据用户ID、项目类型和项目ID查找收藏
     */
    Favorite findByUserIdAndItemTypeAndItemId(Long userId, String itemType, Long itemId);
    
    /**
     * 根据用户ID和项目类型查找收藏列表，按创建时间倒序
     */
    List<Favorite> findByUserIdAndItemTypeOrderByCreateTimeDesc(Long userId, String itemType);
    
    /**
     * 统计用户某类型的收藏数量
     */
    int countByUserIdAndItemType(Long userId, String itemType);
    
    /**
     * 统计用户总收藏数量
     */
    int countByUserId(Long userId);
    
    /**
     * 根据用户ID查找所有收藏
     */
    List<Favorite> findByUserIdOrderByCreateTimeDesc(Long userId);
    
    /**
     * 删除用户的某个收藏
     */
    @Modifying
    @Transactional
    void deleteByUserIdAndItemTypeAndItemId(Long userId, String itemType, Long itemId);
}