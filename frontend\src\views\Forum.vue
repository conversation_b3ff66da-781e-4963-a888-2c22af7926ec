<template>
  <div class="forum-container">
    <Layout>
      <div class="forum-content">
        <!-- 论坛头部 -->
        <div class="forum-header">
          <div class="header-content">
            <h1>校友论坛</h1>
            <p>分享交流，共同成长</p>
          </div>
          <div class="header-actions">
            <el-button type="primary" @click="showCreatePostDialog" v-if="isLoggedIn">
              <i class="el-icon-edit"></i>
              发布帖子
            </el-button>
            <el-button @click="$router.push('/login')" v-else>
              登录后发帖
            </el-button>
          </div>
        </div>

        <!-- 分类导航 -->
        <div class="category-nav">
          <el-tabs v-model="activeCategory" @tab-click="handleCategoryChange">
            <el-tab-pane label="全部" name="all"></el-tab-pane>
            <el-tab-pane label="综合讨论" name="GENERAL"></el-tab-pane>
            <el-tab-pane label="学术交流" name="ACADEMIC"></el-tab-pane>
            <el-tab-pane label="职业发展" name="CAREER"></el-tab-pane>
            <el-tab-pane label="生活分享" name="LIFE"></el-tab-pane>
            <el-tab-pane label="技术讨论" name="TECHNOLOGY"></el-tab-pane>
            <el-tab-pane label="校友动态" name="ALUMNI"></el-tab-pane>
            <el-tab-pane label="问答求助" name="QUESTION"></el-tab-pane>
            <el-tab-pane label="公告通知" name="ANNOUNCEMENT"></el-tab-pane>
          </el-tabs>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-filter">
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索帖子标题或内容..."
              @keyup.enter="searchPosts"
              clearable
            >
              <el-button slot="append" icon="el-icon-search" @click="searchPosts"></el-button>
            </el-input>
          </div>
          <div class="filter-options">
            <el-checkbox v-model="showFeaturedOnly" @change="loadPosts">只看精华</el-checkbox>
            <el-select v-model="sortBy" @change="loadPosts" placeholder="排序方式">
              <el-option label="最新发布" value="latest"></el-option>
              <el-option label="最多点赞" value="likes"></el-option>
              <el-option label="最多评论" value="comments"></el-option>
            </el-select>
          </div>
        </div>

        <!-- 置顶帖子 -->
        <div class="pinned-posts" v-if="pinnedPosts.length > 0">
          <h3><i class="el-icon-top"></i> 置顶帖子</h3>
          <div class="post-list">
            <PostCard 
              v-for="post in pinnedPosts" 
              :key="'pinned-' + post.id" 
              :post="post" 
              :is-pinned="true"
              @click="goToPostDetail(post.id)"
            />
          </div>
        </div>

        <!-- 帖子列表 -->
        <div class="posts-section">
          <div class="section-header">
            <h3>{{ getSectionTitle() }}</h3>
            <span class="post-count">共 {{ totalPosts }} 个帖子</span>
          </div>
          
          <div class="post-list" v-loading="loading">
            <PostCard 
              v-for="post in posts" 
              :key="post.id" 
              :post="post"
              @click="goToPostDetail(post.id)"
            />
          </div>

          <!-- 空状态 -->
          <div class="empty-state" v-if="!loading && posts.length === 0">
            <i class="el-icon-document-remove"></i>
            <p>暂无帖子</p>
            <el-button type="primary" @click="showCreatePostDialog" v-if="isLoggedIn">
              发布第一个帖子
            </el-button>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="totalPosts > 0">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalPosts"
            />
          </div>
        </div>
      </div>
    </Layout>

    <!-- 发布帖子对话框 -->
    <CreatePostDialog 
      :visible.sync="createPostDialogVisible"
      @post-created="handlePostCreated"
    />
  </div>
</template>

<script>
import Layout from '@/components/Layout.vue'
import PostCard from '@/components/forum/PostCard.vue'
import CreatePostDialog from '@/components/forum/CreatePostDialog.vue'
import api from '@/utils/api'

export default {
  name: 'Forum',
  components: {
    Layout,
    PostCard,
    CreatePostDialog
  },
  data() {
    return {
      activeCategory: 'all',
      searchKeyword: '',
      showFeaturedOnly: false,
      sortBy: 'latest',
      posts: [],
      pinnedPosts: [],
      loading: false,
      currentPage: 1,
      pageSize: 10,
      totalPosts: 0,
      createPostDialogVisible: false
    }
  },
  computed: {
    isLoggedIn() {
      return this.$store.state.user && this.$store.state.user.id
    }
  },
  mounted() {
    this.loadPinnedPosts()
    this.loadPosts()
  },
  methods: {
    async loadPosts() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage - 1,
          size: this.pageSize
        }

        if (this.activeCategory !== 'all') {
          params.category = this.activeCategory
        }

        if (this.searchKeyword) {
          params.search = this.searchKeyword
        }

        if (this.showFeaturedOnly) {
          params.featured = true
        }

        const response = await api.get('/posts', { params })
        
        if (response.code === 200) {
          this.posts = response.data.content
          this.totalPosts = response.data.totalElements
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('加载帖子失败:', error)
        this.$message.error('加载帖子失败')
      } finally {
        this.loading = false
      }
    },

    async loadPinnedPosts() {
      try {
        const response = await api.get('/posts/pinned')
        if (response.code === 200) {
          this.pinnedPosts = response.data
        }
      } catch (error) {
        console.error('加载置顶帖子失败:', error)
      }
    },

    handleCategoryChange() {
      this.currentPage = 1
      this.loadPosts()
    },

    searchPosts() {
      this.currentPage = 1
      this.loadPosts()
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadPosts()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadPosts()
    },

    showCreatePostDialog() {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录')
        this.$router.push('/login')
        return
      }
      this.createPostDialogVisible = true
    },

    handlePostCreated() {
      this.createPostDialogVisible = false
      this.loadPosts()
      this.$message.success('帖子发布成功')
    },

    goToPostDetail(postId) {
      this.$router.push(`/forum/posts/${postId}`)
    },

    getSectionTitle() {
      if (this.searchKeyword) {
        return `搜索结果: "${this.searchKeyword}"`
      }
      if (this.showFeaturedOnly) {
        return '精华帖子'
      }
      if (this.activeCategory === 'all') {
        return '最新帖子'
      }
      
      const categoryMap = {
        'GENERAL': '综合讨论',
        'ACADEMIC': '学术交流',
        'CAREER': '职业发展',
        'LIFE': '生活分享',
        'TECHNOLOGY': '技术讨论',
        'ALUMNI': '校友动态',
        'QUESTION': '问答求助',
        'ANNOUNCEMENT': '公告通知'
      }
      
      return categoryMap[this.activeCategory] || '帖子列表'
    }
  }
}
</script>

<style scoped>
.forum-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.forum-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.forum-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 12px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
}

.header-content p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-actions .el-button {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
}

.category-nav {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.search-filter {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.search-box {
  flex: 1;
  max-width: 400px;
}

.filter-options {
  display: flex;
  align-items: center;
  gap: 15px;
}

.pinned-posts {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  border-left: 4px solid #f56c6c;
}

.pinned-posts h3 {
  margin: 0 0 15px 0;
  color: #f56c6c;
  font-size: 18px;
  font-weight: 600;
}

.pinned-posts h3 i {
  margin-right: 8px;
}

.posts-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e8eaec;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.post-count {
  color: #8c9eff;
  font-size: 14px;
}

.post-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #8c9eff;
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 20px;
  display: block;
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e8eaec;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .forum-content {
    padding: 10px;
  }
  
  .forum-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .search-filter {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .filter-options {
    justify-content: center;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>