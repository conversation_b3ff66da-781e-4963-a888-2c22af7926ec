<template>
  <div class="post-detail-container">
    <Layout>
      <div class="post-detail-content" v-loading="loading">
        <!-- 返回按钮 -->
        <div class="back-nav">
          <el-button @click="goBack" icon="el-icon-arrow-left" type="text">
            返回论坛
          </el-button>
        </div>

        <!-- 帖子内容 -->
        <div class="post-content" v-if="post">
          <!-- 帖子头部 -->
          <div class="post-header">
            <div class="post-meta">
              <span class="category-tag" :class="getCategoryClass(post.category)">
                {{ getCategoryName(post.category) }}
              </span>
              <el-tag v-if="post.featured" type="warning" size="small">精华</el-tag>
              <el-tag v-if="post.pinned" type="danger" size="small">置顶</el-tag>
            </div>
            
            <h1 class="post-title">{{ post.title }}</h1>
            
            <div class="post-info">
              <div class="author-info">
                <el-avatar :size="40" :src="post.author && post.author.avatar" icon="el-icon-user-solid"></el-avatar>
                <div class="author-details">
                  <div class="author-name">{{ (post.author && post.author.name) || '匿名用户' }}</div>
                  <div class="post-time">{{ formatTime(post.createdAt) }}</div>
                </div>
              </div>
              
              <div class="post-actions" v-if="canEditPost">
                <el-dropdown @command="handlePostAction">
                  <el-button type="text" icon="el-icon-more"></el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>

          <!-- 帖子正文 -->
          <div class="post-body">
            <div class="post-summary" v-if="post.summary">
              {{ post.summary }}
            </div>
            <div class="post-content-html" v-html="renderedContent"></div>
            
            <!-- 帖子图片 -->
            <div class="post-images" v-if="post.images && post.images.length > 0">
              <el-image
                v-for="(image, index) in post.images"
                :key="index"
                :src="image"
                :preview-src-list="post.images"
                class="post-image"
                fit="cover"
              />
            </div>
          </div>

          <!-- 帖子统计和操作 -->
          <div class="post-stats">
            <div class="stats-left">
              <div class="stat-item">
                <i class="el-icon-view"></i>
                <span>{{ formatNumber(post.viewCount) }} 浏览</span>
              </div>
              <div class="stat-item like-stat" :class="{ 'liked': isLiked }" @click="toggleLike">
                <i class="el-icon-thumb"></i>
                <span>{{ formatNumber(post.likeCount) }} 点赞</span>
              </div>
              <div class="stat-item">
                <i class="el-icon-chat-dot-round"></i>
                <span>{{ formatNumber(post.commentCount) }} 评论</span>
              </div>
            </div>
            
            <div class="stats-right">
              <el-button @click="sharePost" type="text" icon="el-icon-share">分享</el-button>
              <el-button @click="favoritePost" type="text" :icon="isFavorited ? 'el-icon-star-on' : 'el-icon-star-off'">
                {{ isFavorited ? '已收藏' : '收藏' }}
              </el-button>
            </div>
          </div>
        </div>

        <!-- 评论区域 -->
        <div class="comments-section" v-if="post">
          <div class="comments-header">
            <h3>评论 ({{ comments.length }})</h3>
            <el-button v-if="isLoggedIn" @click="showCommentEditor = !showCommentEditor" type="primary" size="small">
              {{ showCommentEditor ? '取消评论' : '写评论' }}
            </el-button>
          </div>

          <!-- 评论编辑器 -->
          <div class="comment-editor" v-if="showCommentEditor">
            <el-input
              v-model="newComment"
              type="textarea"
              :rows="4"
              placeholder="写下你的评论..."
              maxlength="1000"
              show-word-limit
            />
            <div class="editor-actions">
              <el-button @click="showCommentEditor = false">取消</el-button>
              <el-button type="primary" @click="submitComment" :loading="submittingComment">
                发布评论
              </el-button>
            </div>
          </div>

          <!-- 评论列表 -->
          <div class="comments-list" v-loading="loadingComments">
            <CommentItem
              v-for="comment in comments"
              :key="comment.id"
              :comment="comment"
              @reply="handleReply"
              @delete="handleDeleteComment"
            />
            
            <div class="empty-comments" v-if="!loadingComments && comments.length === 0">
              <i class="el-icon-chat-dot-round"></i>
              <p>暂无评论，来发表第一个评论吧！</p>
            </div>
          </div>

          <!-- 加载更多评论 -->
          <div class="load-more" v-if="hasMoreComments">
            <el-button @click="loadMoreComments" :loading="loadingMoreComments">
              加载更多评论
            </el-button>
          </div>
        </div>
      </div>
    </Layout>

    <!-- 编辑帖子对话框 -->
    <CreatePostDialog
      :visible.sync="editDialogVisible"
      :edit-post="post"
      @post-created="handlePostUpdated"
    />
  </div>
</template>

<script>
import Layout from '@/components/Layout.vue'
import CommentItem from '@/components/forum/CommentItem.vue'
import CreatePostDialog from '@/components/forum/CreatePostDialog.vue'
import api from '@/utils/api'
import { marked } from 'marked'

export default {
  name: 'PostDetail',
  components: {
    Layout,
    CommentItem,
    CreatePostDialog
  },
  data() {
    return {
      post: null,
      comments: [],
      loading: false,
      loadingComments: false,
      loadingMoreComments: false,
      submittingComment: false,
      showCommentEditor: false,
      newComment: '',
      isLiked: false,
      isFavorited: false,
      hasMoreComments: false,
      commentPage: 0,
      editDialogVisible: false
    }
  },
  computed: {
    isLoggedIn() {
      return this.$store.state.user && this.$store.state.user.id
    },
    canEditPost() {
      if (!this.isLoggedIn || !this.post) return false
      return (this.post.author && this.post.author.id === this.$store.state.user.id) || this.$store.state.user.role === 'ADMIN'
    },
    renderedContent() {
      if (!this.post || !this.post.content) return ''
      try {
        return marked(this.post.content)
      } catch (error) {
        return this.post.content
      }
    }
  },
  async mounted() {
    await this.loadPost()
    await this.loadComments()
    if (this.isLoggedIn) {
      await this.checkLikeStatus()
      await this.checkFavoriteStatus()
    }
  },
  methods: {
    async loadPost() {
      this.loading = true
      try {
        const postId = this.$route.params.id
        const response = await api.get(`/posts/${postId}`)
        
        if (response.code === 200) {
          this.post = response.data
          // 增加浏览量
          this.incrementViewCount()
        } else {
          this.$message.error(response.message)
          this.$router.push('/forum')
        }
      } catch (error) {
        console.error('加载帖子失败:', error)
        this.$message.error('加载帖子失败')
        this.$router.push('/forum')
      } finally {
        this.loading = false
      }
    },

    async loadComments() {
      this.loadingComments = true
      try {
        const postId = this.$route.params.id
        const response = await api.get(`/comments/post/${postId}`)
        
        if (response.code === 200) {
          this.comments = response.data
        }
      } catch (error) {
        console.error('加载评论失败:', error)
      } finally {
        this.loadingComments = false
      }
    },

    async incrementViewCount() {
      try {
        await api.post(`/posts/${this.post.id}/view`)
      } catch (error) {
        console.error('增加浏览量失败:', error)
      }
    },

    async checkLikeStatus() {
      try {
        const response = await api.get(`/post-likes/posts/${this.post.id}/liked`)
        if (response.code === 200) {
          this.isLiked = response.data
        }
      } catch (error) {
        console.error('检查点赞状态失败:', error)
      }
    },

    async checkFavoriteStatus() {
      try {
        const response = await api.get(`/favorites/posts/${this.post.id}/favorited`)
        if (response.code === 200) {
          this.isFavorited = response.data
        }
      } catch (error) {
        console.error('检查收藏状态失败:', error)
      }
    },

    async toggleLike() {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录')
        return
      }

      try {
        let response
        if (this.isLiked) {
          response = await api.delete(`/post-likes/posts/${this.post.id}/like`)
        } else {
          response = await api.post(`/post-likes/posts/${this.post.id}/like`)
        }

        if (response.code === 200) {
          this.isLiked = !this.isLiked
          this.post.likeCount += this.isLiked ? 1 : -1
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('点赞操作失败:', error)
        this.$message.error('操作失败')
      }
    },

    async favoritePost() {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录')
        return
      }

      try {
        let response
        if (this.isFavorited) {
          response = await api.delete(`/favorites/posts/${this.post.id}`)
        } else {
          response = await api.post(`/favorites/posts/${this.post.id}`)
        }

        if (response.code === 200) {
          this.isFavorited = !this.isFavorited
          this.$message.success(this.isFavorited ? '收藏成功' : '取消收藏成功')
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        this.$message.error('操作失败')
      }
    },

    async submitComment() {
      if (!this.newComment.trim()) {
        this.$message.warning('请输入评论内容')
        return
      }

      this.submittingComment = true
      try {
        const response = await api.post('/comments', {
          postId: this.post.id,
          content: this.newComment
        })

        if (response.code === 200) {
          this.comments.unshift(response.data)
          this.post.commentCount++
          this.newComment = ''
          this.showCommentEditor = false
          this.$message.success('评论发布成功')
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('发布评论失败:', error)
        this.$message.error('发布评论失败')
      } finally {
        this.submittingComment = false
      }
    },

    handleReply(comment) {
      this.showCommentEditor = true
      this.newComment = `@${(comment.author && comment.author.name) || '匿名用户'} `
    },

    async handleDeleteComment(commentId) {
      try {
        const response = await api.delete(`/comments/${commentId}`)
        if (response.code === 200) {
          this.comments = this.comments.filter(c => c.id !== commentId)
          this.post.commentCount--
          this.$message.success('评论删除成功')
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('删除评论失败:', error)
        this.$message.error('删除评论失败')
      }
    },

    handlePostAction(command) {
      if (command === 'edit') {
        this.editDialogVisible = true
      } else if (command === 'delete') {
        this.deletePost()
      }
    },

    async deletePost() {
      this.$confirm('确定要删除这个帖子吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await api.delete(`/posts/${this.post.id}`)
          if (response.code === 200) {
            this.$message.success('帖子删除成功')
            this.$router.push('/forum')
          } else {
            this.$message.error(response.message)
          }
        } catch (error) {
          console.error('删除帖子失败:', error)
          this.$message.error('删除帖子失败')
        }
      })
    },

    handlePostUpdated(updatedPost) {
      this.post = updatedPost
      this.editDialogVisible = false
      this.$message.success('帖子更新成功')
    },

    sharePost() {
      const url = window.location.href
      if (navigator.share) {
        navigator.share({
          title: this.post.title,
          text: this.post.summary || this.post.title,
          url: url
        })
      } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(url).then(() => {
          this.$message.success('链接已复制到剪贴板')
        })
      }
    },

    goBack() {
      this.$router.go(-1)
    },

    getCategoryName(category) {
      const categoryMap = {
        'GENERAL': '综合讨论',
        'ACADEMIC': '学术交流',
        'CAREER': '职场分享',
        'LIFE': '生活感悟',
        'TECH': '技术讨论',
        'ACTIVITY': '活动通知',
        'HELP': '求助问答',
        'OTHER': '其他'
      }
      return categoryMap[category] || '未分类'
    },

    getCategoryClass(category) {
      const classMap = {
        'GENERAL': 'category-general',
        'ACADEMIC': 'category-academic',
        'CAREER': 'category-career',
        'LIFE': 'category-life',
        'TECH': 'category-tech',
        'ACTIVITY': 'category-activity',
        'HELP': 'category-help',
        'OTHER': 'category-other'
      }
      return classMap[category] || 'category-default'
    },

    formatTime(time) {
      if (!time) return ''
      
      const now = new Date()
      const postTime = new Date(time)
      const diff = now - postTime
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      
      return postTime.toLocaleDateString('zh-CN')
    },

    formatNumber(num) {
      if (!num) return '0'
      if (num < 1000) return num.toString()
      if (num < 10000) return (num / 1000).toFixed(1) + 'k'
      return (num / 10000).toFixed(1) + 'w'
    }
  }
}
</script>

<style scoped>
.post-detail-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.post-detail-content {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.back-nav {
  margin-bottom: 20px;
}

.post-content {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.post-header {
  margin-bottom: 30px;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.category-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.category-general { background: #909399; }
.category-academic { background: #409eff; }
.category-career { background: #67c23a; }
.category-life { background: #e6a23c; }
.category-tech { background: #f56c6c; }
.category-activity { background: #909399; }
.category-help { background: #f56c6c; }
.category-other { background: #c0c4cc; }

.post-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.post-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.author-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
}

.post-time {
  color: #8c9eff;
  font-size: 14px;
}

.post-body {
  margin-bottom: 30px;
}

.post-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-style: italic;
  color: #666;
  border-left: 4px solid #409eff;
}

.post-content-html {
  line-height: 1.8;
  color: #2c3e50;
  font-size: 16px;
}

.post-content-html >>> h1,
.post-content-html >>> h2,
.post-content-html >>> h3 {
  margin-top: 30px;
  margin-bottom: 15px;
  color: #2c3e50;
}

.post-content-html >>> p {
  margin-bottom: 15px;
}

.post-content-html >>> code {
  background: #f1f1f1;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.post-content-html >>> pre {
  background: #f8f8f8;
  padding: 15px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 15px 0;
}

.post-content-html >>> blockquote {
  border-left: 4px solid #ddd;
  margin: 15px 0;
  padding-left: 20px;
  color: #666;
  font-style: italic;
}

.post-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.post-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  cursor: pointer;
}

.post-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #e8eaec;
}

.stats-left {
  display: flex;
  align-items: center;
  gap: 25px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #8c9eff;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.2s ease;
}

.stat-item:hover {
  color: #409eff;
}

.stat-item i {
  font-size: 16px;
}

.like-stat.liked {
  color: #f56c6c;
}

.stats-right {
  display: flex;
  gap: 10px;
}

.comments-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e8eaec;
}

.comments-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.comment-editor {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.editor-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.empty-comments {
  text-align: center;
  padding: 60px 20px;
  color: #8c9eff;
}

.empty-comments i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.load-more {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e8eaec;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .post-detail-content {
    padding: 10px;
  }
  
  .post-content,
  .comments-section {
    padding: 20px;
  }
  
  .post-title {
    font-size: 24px;
  }
  
  .post-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .post-stats {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .stats-left {
    justify-content: space-around;
  }
  
  .stats-right {
    justify-content: center;
  }
  
  .comments-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .post-images {
    grid-template-columns: 1fr;
  }
}
</style>