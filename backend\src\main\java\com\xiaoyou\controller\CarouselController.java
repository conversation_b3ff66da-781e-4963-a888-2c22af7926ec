package com.xiaoyou.controller;

import com.xiaoyou.dto.ApiResponse;
import com.xiaoyou.entity.Carousel;
import com.xiaoyou.service.CarouselService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/carousels")
@CrossOrigin(origins = {"http://localhost:8081", "http://localhost:3000"})
public class CarouselController {
    
    @Autowired
    private CarouselService carouselService;
    
    /**
     * 获取所有启用的轮播图（公开接口，供前台展示使用）
     */
    @GetMapping("/active")
    public ApiResponse<List<Carousel>> getActiveCarousels() {
        try {
            List<Carousel> carousels = carouselService.getActiveCarousels();
            return ApiResponse.success("获取轮播图成功", carousels);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取轮播图失败: " + e.getMessage());
        }
    }
}