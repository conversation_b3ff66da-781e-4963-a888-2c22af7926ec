package com.xiaoyou.controller;

import com.xiaoyou.dto.ApiResponse;
import com.xiaoyou.entity.Favorite;
import com.xiaoyou.service.FavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/api/favorites")
@CrossOrigin(origins = "http://localhost:8081")
public class FavoriteController {
    
    @Autowired
    private FavoriteService favoriteService;
    
    @PostMapping("/add")
    public ApiResponse<String> addFavorite(@RequestParam Long userId, 
                                          @RequestParam Long itemId, 
                                          @RequestParam String itemType) {
        try {
            System.out.println("添加收藏请求: userId=" + userId + ", itemId=" + itemId + ", itemType=" + itemType);
            boolean result = favoriteService.addFavorite(userId, itemId, itemType);
            if (result) {
                return ApiResponse.success("收藏成功");
            } else {
                return ApiResponse.error("该项目已经收藏过了");
            }
        } catch (Exception e) {
            System.err.println("添加收藏失败: " + e.getMessage());
            e.printStackTrace();
            return ApiResponse.error("添加收藏失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/remove")
    public ApiResponse<String> removeFavorite(@RequestParam Long userId, 
                                             @RequestParam Long itemId, 
                                             @RequestParam String itemType) {
        try {
            System.out.println("取消收藏请求: userId=" + userId + ", itemId=" + itemId + ", itemType=" + itemType);
            boolean result = favoriteService.removeFavorite(userId, itemId, itemType);
            if (result) {
                return ApiResponse.success("取消收藏成功");
            } else {
                return ApiResponse.error("取消收藏失败，该项目未收藏");
            }
        } catch (Exception e) {
            System.err.println("取消收藏失败: " + e.getMessage());
            e.printStackTrace();
            return ApiResponse.error("取消收藏失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/check")
    public ApiResponse<Boolean> checkFavorite(@RequestParam Long userId, 
                                             @RequestParam Long itemId, 
                                             @RequestParam String itemType) {
        try {
            System.out.println("检查收藏状态请求: userId=" + userId + ", itemId=" + itemId + ", itemType=" + itemType);
            boolean isFavorited = favoriteService.isFavorited(userId, itemId, itemType);
            return ApiResponse.success(isFavorited);
        } catch (Exception e) {
            System.err.println("检查收藏状态失败: " + e.getMessage());
            e.printStackTrace();
            return ApiResponse.error("检查收藏状态失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/list")
    public ApiResponse<List<Favorite>> getFavoriteList(@RequestParam Long userId, 
                                                      @RequestParam(required = false) String itemType) {
        try {
            List<Favorite> favorites = favoriteService.getFavoritesByUser(userId, itemType);
            return ApiResponse.success(favorites);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @GetMapping("/count")
    public ApiResponse<Long> getFavoriteCount(@RequestParam Long userId) {
        try {
            long count = favoriteService.getFavoriteCount(userId);
            return ApiResponse.success(count);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    // 前端适配的收藏接口
    
    // 检查帖子收藏状态
    @GetMapping("/posts/{postId}/favorited")
    public ApiResponse<Boolean> isPostFavorited(@PathVariable Long postId, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.success("未登录", false);
            }
            
            boolean isFavorited = favoriteService.isFavorited(userId, postId, "news");
            return ApiResponse.success("查询成功", isFavorited);
        } catch (Exception e) {
            return ApiResponse.error(500, "查询失败: " + e.getMessage());
        }
    }
    
    // 收藏帖子
    @PostMapping("/posts/{postId}")
    public ApiResponse<String> favoritePost(@PathVariable Long postId, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.error(401, "请先登录");
            }
            
            boolean result = favoriteService.addFavorite(userId, postId, "news");
            if (result) {
                return ApiResponse.success("收藏成功");
            } else {
                return ApiResponse.error(400, "该帖子已经收藏过了");
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "收藏失败: " + e.getMessage());
        }
    }
    
    // 取消收藏帖子
    @DeleteMapping("/posts/{postId}")
    public ApiResponse<String> unfavoritePost(@PathVariable Long postId, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.error(401, "请先登录");
            }
            
            boolean result = favoriteService.removeFavorite(userId, postId, "news");
            if (result) {
                return ApiResponse.success("取消收藏成功");
            } else {
                return ApiResponse.error(400, "该帖子未收藏");
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "取消收藏失败: " + e.getMessage());
        }
    }
    
    // 辅助方法：获取当前用户ID
    private Long getCurrentUserId(HttpServletRequest request) {
        String userIdHeader = request.getHeader("User-Id");
        if (userIdHeader != null) {
            try {
                return Long.parseLong(userIdHeader);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}