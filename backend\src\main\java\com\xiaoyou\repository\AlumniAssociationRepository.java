package com.xiaoyou.repository;

import com.xiaoyou.entity.AlumniAssociation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AlumniAssociationRepository extends JpaRepository<AlumniAssociation, Long> {
    
    // 查找所有活跃的校友会，按创建时间倒序
    Page<AlumniAssociation> findByActiveTrueOrderByCreateTimeDesc(Pageable pageable);
    
    // 根据名称搜索活跃的校友会
    Page<AlumniAssociation> findByNameContainingIgnoreCaseAndActiveTrueOrderByCreateTimeDesc(String name, Pageable pageable);
    
    // 根据地区搜索活跃的校友会
    Page<AlumniAssociation> findByAddressContainingIgnoreCaseAndActiveTrueOrderByCreateTimeDesc(String address, Pageable pageable);
    
    // 查找所有校友会（管理员用）
    Page<AlumniAssociation> findAllByOrderByCreateTimeDesc(Pageable pageable);
    
    // 统计活跃校友会数量
    long countByActiveTrue();
    
    // 统计总成员数
    @Query("SELECT SUM(a.memberCount) FROM AlumniAssociation a WHERE a.active = true")
    Long getTotalMemberCount();
    
    // 查找热门校友会（按成员数排序）
    @Query("SELECT a FROM AlumniAssociation a WHERE a.active = true ORDER BY a.memberCount DESC")
    List<AlumniAssociation> findTopByMemberCount(Pageable pageable);
}