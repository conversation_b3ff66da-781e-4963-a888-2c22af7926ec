<template>
  <Layout>
    <div class="activity-detail-container">
      <div class="activity-detail" v-loading="loading">
        <div v-if="activity" class="activity-content">
          <!-- 返回按钮 -->
          <div class="back-button">
            <el-button 
              type="primary" 
              plain 
              icon="el-icon-arrow-left"
              @click="goBack"
            >
              返回活动列表
            </el-button>
          </div>

          <!-- 活动头部 -->
          <div class="activity-header">
            <div class="activity-image">
              <img :src="activity.imageUrl || '/images/default-activity.jpg'" :alt="activity.title" @error="handleImageError" />
              <div class="activity-status" :class="getStatusClass(activity.status)">
                {{ getStatusText(activity.status) }}
              </div>
            </div>
            
            <div class="activity-info">
              <div class="activity-type">{{ getTypeText(activity.type) }}</div>
              <h1 class="activity-title">{{ activity.title }}</h1>
              
              <div class="activity-meta">
                <div class="meta-item">
                  <i class="el-icon-user-solid"></i>
                  <span>组织者：{{ activity.organizer || '校友会' }}</span>
                </div>
                <div class="meta-item">
                  <i class="el-icon-view"></i>
                  <span>浏览量：{{ activity.viewCount }}</span>
                </div>
                <div class="meta-item">
                  <i class="el-icon-time"></i>
                  <span>发布时间：{{ formatDate(activity.publishTime || activity.createTime) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 活动摘要 -->
          <div class="activity-summary" v-if="activity.summary">
            <div class="summary-box">
              <h3>活动简介</h3>
              <p>{{ activity.summary }}</p>
            </div>
          </div>

          <!-- 活动详情信息 -->
          <div class="activity-details-info">
            <div class="details-grid">
              <div class="detail-card">
                <i class="el-icon-time"></i>
                <div class="detail-content">
                  <h4>活动时间</h4>
                  <p>{{ formatDateTime(activity.startTime) }} - {{ formatDateTime(activity.endTime) }}</p>
                </div>
              </div>
              
              <div class="detail-card">
                <i class="el-icon-location"></i>
                <div class="detail-content">
                  <h4>活动地点</h4>
                  <p>{{ activity.location }}</p>
                </div>
              </div>
              
              <div class="detail-card">
                <i class="el-icon-user"></i>
                <div class="detail-content">
                  <h4>参与人数</h4>
                  <p>{{ activity.currentParticipants }}/{{ activity.maxParticipants }}人</p>
                </div>
              </div>
              
              <div class="detail-card">
                <i class="el-icon-money"></i>
                <div class="detail-content">
                  <h4>活动费用</h4>
                  <p v-if="activity.fee > 0">¥{{ activity.fee }}</p>
                  <p v-else>免费</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 活动描述 -->
          <div class="activity-description">
            <h3>活动详情</h3>
            <div class="content" v-html="formatContent(activity.description)"></div>
          </div>

          <!-- 报名按钮 -->
          <div class="activity-actions" v-if="activity.status === 'UPCOMING'">
            <el-button 
              type="primary" 
              size="large"
              :disabled="activity.currentParticipants >= activity.maxParticipants"
              :loading="joinLoading"
              @click="joinActivity"
              v-if="!isJoined"
            >
              <i class="el-icon-plus"></i>
              {{ activity.currentParticipants >= activity.maxParticipants ? '报名已满' : '立即报名' }}
            </el-button>
            
            <el-button 
              type="warning" 
              size="large"
              :loading="joinLoading"
              @click="leaveActivity"
              v-else
            >
              <i class="el-icon-minus"></i>
              取消报名
            </el-button>
            
            <el-button 
              type="success" 
              size="large"
              icon="el-icon-share"
              @click="shareActivity"
            >
              分享活动
            </el-button>
            
            <el-button 
              :type="isFavorited ? 'warning' : 'info'" 
              size="large"
              :icon="isFavorited ? 'el-icon-star-on' : 'el-icon-collection'"
              :loading="favoriteLoading"
              @click="collectActivity"
            >
              {{ isFavorited ? '已收藏' : '收藏' }}
            </el-button>
          </div>
          
          <div class="activity-actions" v-else-if="activity.status === 'ONGOING'">
            <el-alert
              title="活动正在进行中"
              type="info"
              :closable="false"
              show-icon>
            </el-alert>
          </div>
          
          <div class="activity-actions" v-else-if="activity.status === 'COMPLETED'">
            <el-alert
              title="活动已结束"
              type="success"
              :closable="false"
              show-icon>
            </el-alert>
          </div>
        </div>

        <div v-else-if="!loading" class="error-content">
          <el-result
            icon="error"
            title="活动不存在"
            sub-title="抱歉，您访问的活动不存在或已被删除"
          >
            <template slot="extra">
              <el-button type="primary" @click="goBack">返回活动列表</el-button>
            </template>
          </el-result>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import api from '@/utils/api'
import Layout from '@/components/Layout.vue'

export default {
  name: 'ActivityDetail',
  components: {
    Layout
  },
  data() {
    return {
      activity: null,
      loading: false,
      joinLoading: false,
      isJoined: false,
      isFavorited: false,
      favoriteLoading: false
    }
  },
  computed: {
    currentUser() {
      return this.$store.state.user
    },
    isLoggedIn() {
      return this.$store.state.isLoggedIn
    }
  },
  mounted() {
    this.fetchActivityDetail()
  },
  watch: {
    '$route'() {
      this.fetchActivityDetail()
    }
  },
  methods: {
    async fetchActivityDetail() {
      const activityId = this.$route.params.id
      if (!activityId) {
        this.$message.error('活动ID不存在')
        return
      }

      this.loading = true
      try {
        const response = await api.get(`/activities/${activityId}`)
        
        if (response.code === 200) {
          this.activity = response.data
          // 获取活动详情后检查收藏状态
          if (this.isLoggedIn) {
            this.checkFavoriteStatus()
            // 这里可以添加检查是否已报名的逻辑
          }
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('获取活动详情失败:', error)
        this.$message.error('获取活动详情失败')
      } finally {
        this.loading = false
      }
    },
    
    async joinActivity() {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录后再报名')
        this.$router.push('/login')
        return
      }
      
      this.joinLoading = true
      try {
        const response = await api.post(`/activities/${this.activity.id}/join`)
        
        if (response.code === 200) {
          this.$message.success('报名成功')
          this.isJoined = true
          this.activity.currentParticipants++
        } else {
          this.$message.error(response.message || '报名失败')
        }
      } catch (error) {
        console.error('报名失败:', error)
        this.$message.error('报名失败，请稍后重试')
      } finally {
        this.joinLoading = false
      }
    },
    
    async leaveActivity() {
      this.joinLoading = true
      try {
        const response = await api.post(`/activities/${this.activity.id}/leave`)
        
        if (response.code === 200) {
          this.$message.success('取消报名成功')
          this.isJoined = false
          this.activity.currentParticipants--
        } else {
          this.$message.error(response.message || '取消报名失败')
        }
      } catch (error) {
        console.error('取消报名失败:', error)
        this.$message.error('取消报名失败，请稍后重试')
      } finally {
        this.joinLoading = false
      }
    },
    
    async checkFavoriteStatus() {
      if (!this.currentUser || !this.activity) return
      
      try {
        const response = await api.get('/favorites/check', {
          params: {
            userId: this.currentUser.id,
            itemId: this.activity.id,
            itemType: 'activity'
          }
        })
        
        if (response.code === 200) {
          this.isFavorited = response.data
        }
      } catch (error) {
        console.error('检查收藏状态失败:', error)
      }
    },
    
    async collectActivity() {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录后再收藏')
        this.$router.push('/login')
        return
      }
      
      this.favoriteLoading = true
      
      try {
        if (this.isFavorited) {
          const response = await api.delete('/favorites/remove', {
            params: {
              userId: this.currentUser.id,
              itemId: this.activity.id,
              itemType: 'activity'
            }
          })
          
          if (response.code === 200) {
            this.isFavorited = false
            this.$message.success('取消收藏成功')
          } else {
            this.$message.error(response.message || '取消收藏失败')
          }
        } else {
          const response = await api.post('/favorites/add', null, {
            params: {
              userId: this.currentUser.id,
              itemId: this.activity.id,
              itemType: 'activity'
            }
          })
          
          if (response.code === 200) {
            this.isFavorited = true
            this.$message.success('收藏成功')
          } else {
            this.$message.error(response.message || '收藏失败')
          }
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        this.$message.error('操作失败，请稍后重试')
      } finally {
        this.favoriteLoading = false
      }
    },
    
    shareActivity() {
      const url = window.location.href
      if (navigator.share) {
        navigator.share({
          title: this.activity.title,
          text: this.activity.summary,
          url: url
        })
      } else {
        navigator.clipboard.writeText(url).then(() => {
          this.$message.success('链接已复制到剪贴板')
        }).catch(() => {
          this.$message.error('复制失败，请手动复制链接')
        })
      }
    },
    
    goBack() {
      this.$router.push('/activities')
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    formatDateTime(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    formatContent(content) {
      if (!content) return ''
      return content.replace(/\n/g, '<br>')
    },
    
    getStatusText(status) {
      const statusMap = {
        'UPCOMING': '即将开始',
        'ONGOING': '进行中',
        'COMPLETED': '已结束',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || status
    },
    
    getStatusClass(status) {
      return `status-${status.toLowerCase()}`
    },
    
    getTypeText(type) {
      const typeMap = {
        'CONFERENCE': '会议',
        'SEMINAR': '研讨会',
        'NETWORKING': '联谊活动',
        'CHARITY': '公益活动',
        'SPORTS': '体育活动',
        'CULTURAL': '文化活动',
        'OTHER': '其他'
      }
      return typeMap[type] || type
    },
    
    handleImageError(e) {
      e.target.src = '/images/default-activity.jpg'
    }
  }
}
</script>

<style scoped>
.activity-detail-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.back-button {
  margin-bottom: 20px;
}

.activity-header {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.activity-image {
  flex: 0 0 300px;
  height: 200px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.activity-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.activity-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.status-upcoming {
  background-color: #409eff;
}

.status-ongoing {
  background-color: #67c23a;
}

.status-completed {
  background-color: #909399;
}

.status-cancelled {
  background-color: #f56c6c;
}

.activity-info {
  flex: 1;
}

.activity-type {
  display: inline-block;
  background-color: #f0f9ff;
  color: #409eff;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  margin-bottom: 10px;
}

.activity-title {
  font-size: 28px;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 20px;
}

.activity-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.meta-item i {
  color: #409eff;
}

.activity-summary {
  margin-bottom: 30px;
}

.summary-box {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.summary-box h3 {
  color: #409eff;
  margin: 0 0 10px 0;
  font-size: 16px;
}

.summary-box p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.activity-details-info {
  margin-bottom: 30px;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.detail-card {
  background: white;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.detail-card i {
  font-size: 24px;
  color: #409eff;
}

.detail-content h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 14px;
}

.detail-content p {
  margin: 0;
  color: #666;
  font-size: 13px;
}

.activity-description {
  margin-bottom: 40px;
}

.activity-description h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.content {
  font-size: 16px;
  line-height: 1.8;
  color: #333;
}

.content::deep h1,
.content::deep h2,
.content::deep h3 {
  margin: 20px 0 10px 0;
  color: #2c3e50;
}

.content::deep p {
  margin-bottom: 15px;
}

.activity-actions {
  text-align: center;
  padding: 30px 0;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.activity-actions .el-button {
  min-width: 120px;
}

.error-content {
  padding: 40px 0;
}

@media (max-width: 768px) {
  .activity-header {
    flex-direction: column;
  }
  
  .activity-image {
    flex: none;
    height: 250px;
  }
  
  .activity-title {
    font-size: 24px;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .activity-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>