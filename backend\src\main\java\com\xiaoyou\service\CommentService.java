package com.xiaoyou.service;

import com.xiaoyou.dto.CommentRequest;
import com.xiaoyou.entity.Comment;
import com.xiaoyou.entity.Post;
import com.xiaoyou.entity.User;
import com.xiaoyou.repository.CommentRepository;
import com.xiaoyou.repository.PostRepository;
import com.xiaoyou.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CommentService {
    
    @Autowired
    private CommentRepository commentRepository;
    
    @Autowired
    private PostRepository postRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    // 获取帖子的评论
    public List<Comment> getCommentsByPostId(Long postId) {
        return commentRepository.findTopLevelCommentsByPostId(postId);
    }
    
    // 获取评论的回复
    public List<Comment> getRepliesByParentId(Long parentId) {
        return commentRepository.findRepliesByParentId(parentId);
    }
    
    // 创建评论
    @Transactional
    public Comment createComment(CommentRequest commentRequest, Long authorId) {
        User author = userRepository.findById(authorId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        Post post = postRepository.findById(commentRequest.getPostId())
                .orElseThrow(() -> new RuntimeException("帖子不存在"));
        
        Comment comment = new Comment();
        comment.setContent(commentRequest.getContent());
        comment.setPost(post);
        comment.setAuthor(author);
        
        // 如果是回复评论
        if (commentRequest.getParentId() != null) {
            Comment parentComment = commentRepository.findById(commentRequest.getParentId())
                    .orElseThrow(() -> new RuntimeException("父评论不存在"));
            comment.setParent(parentComment);
        }
        
        Comment savedComment = commentRepository.save(comment);
        
        // 增加帖子评论数
        postRepository.incrementCommentCount(post.getId());
        
        return savedComment;
    }
    
    // 更新评论
    public Comment updateComment(Long id, CommentRequest commentRequest, Long userId) {
        Comment comment = commentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("评论不存在"));
        
        // 检查权限：只有作者或管理员可以编辑
        User currentUser = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        if (!comment.getAuthor().getId().equals(userId) && !currentUser.getRole().equals(User.Role.ADMIN)) {
            throw new RuntimeException("没有权限编辑此评论");
        }
        
        comment.setContent(commentRequest.getContent());
        return commentRepository.save(comment);
    }
    
    // 删除评论
    @Transactional
    public void deleteComment(Long id, Long userId) {
        Comment comment = commentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("评论不存在"));
        
        // 检查权限：只有作者或管理员可以删除
        User currentUser = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        if (!comment.getAuthor().getId().equals(userId) && !currentUser.getRole().equals(User.Role.ADMIN)) {
            throw new RuntimeException("没有权限删除此评论");
        }
        
        // 减少帖子评论数
        postRepository.decrementCommentCount(comment.getPost().getId());
        
        commentRepository.deleteById(id);
    }
    
    // 获取用户的评论
    public Page<Comment> getUserComments(Long userId, Pageable pageable) {
        return commentRepository.findCommentsByAuthor(userId, pageable);
    }
    
    // 管理员获取所有评论
    public Page<Comment> getAllCommentsForAdmin(Pageable pageable) {
        return commentRepository.findAllCommentsForAdmin(pageable);
    }
    
    // 根据状态获取评论
    public Page<Comment> getCommentsByStatus(String status, Pageable pageable) {
        return commentRepository.findCommentsByStatus(status, pageable);
    }
    
    // 更改评论状态
    @Transactional
    public Comment updateCommentStatus(Long id, String status) {
        Comment comment = commentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("评论不存在"));
        
        comment.setStatus(status);
        return commentRepository.save(comment);
    }
    
    // 统计评论数量
    public Long countCommentsByStatus(String status) {
        return commentRepository.countCommentsByStatus(status);
    }
    
    // 统计帖子评论数量
    public Long countCommentsByPostId(Long postId) {
        return commentRepository.countCommentsByPostId(postId);
    }
}