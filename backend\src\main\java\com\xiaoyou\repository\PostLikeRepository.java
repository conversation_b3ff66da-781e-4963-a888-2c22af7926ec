package com.xiaoyou.repository;

import com.xiaoyou.entity.PostLike;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PostLikeRepository extends JpaRepository<PostLike, Long> {
    
    // 检查用户是否已点赞帖子
    @Query("SELECT pl FROM PostLike pl WHERE pl.post.id = :postId AND pl.user.id = :userId")
    Optional<PostLike> findByPostIdAndUserId(@Param("postId") Long postId, @Param("userId") Long userId);
    
    // 获取帖子的所有点赞
    @Query("SELECT pl FROM PostLike pl WHERE pl.post.id = :postId ORDER BY pl.createTime DESC")
    List<PostLike> findByPostId(@Param("postId") Long postId);
    
    // 获取用户的所有点赞
    @Query("SELECT pl FROM PostLike pl WHERE pl.user.id = :userId ORDER BY pl.createTime DESC")
    List<PostLike> findByUserId(@Param("userId") Long userId);
    
    // 统计帖子的点赞数量
    @Query("SELECT COUNT(pl) FROM PostLike pl WHERE pl.post.id = :postId")
    Long countByPostId(@Param("postId") Long postId);
    
    // 统计用户的点赞数量
    @Query("SELECT COUNT(pl) FROM PostLike pl WHERE pl.user.id = :userId")
    Long countByUserId(@Param("userId") Long userId);
    
    // 删除帖子的所有点赞
    void deleteByPostId(Long postId);
    
    // 删除用户对帖子的点赞
    void deleteByPostIdAndUserId(Long postId, Long userId);
}