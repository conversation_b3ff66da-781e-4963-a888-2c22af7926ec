package com.xiaoyou.controller;

import com.xiaoyou.dto.ActivityRequest;
import com.xiaoyou.dto.ApiResponse;
import com.xiaoyou.entity.Activity;
import com.xiaoyou.service.ActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/activities")
@Validated
public class ActivityController {
    
    @Autowired
    private ActivityService activityService;
    
    @GetMapping
    public ApiResponse<Page<Activity>> getPublishedActivities(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Activity> activitiesPage;
            
            if (search != null && !search.trim().isEmpty()) {
                activitiesPage = activityService.searchActivities(search, pageable);
            } else if (type != null && !type.trim().isEmpty()) {
                Activity.ActivityType activityType = Activity.ActivityType.valueOf(type.toUpperCase());
                activitiesPage = activityService.getActivitiesByType(activityType, pageable);
            } else if (status != null && !status.trim().isEmpty()) {
                Activity.ActivityStatus activityStatus = Activity.ActivityStatus.valueOf(status.toUpperCase());
                activitiesPage = activityService.getActivitiesByStatus(activityStatus, pageable);
            } else {
                activitiesPage = activityService.getAllPublishedActivities(pageable);
            }
            
            return ApiResponse.success(activitiesPage);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @GetMapping("/upcoming")
    public ApiResponse<List<Activity>> getUpcomingActivities() {
        try {
            List<Activity> activities = activityService.getUpcomingActivities();
            return ApiResponse.success(activities);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @GetMapping("/popular")
    public ApiResponse<List<Activity>> getPopularActivities(
            @RequestParam(defaultValue = "5") int limit) {
        try {
            List<Activity> activities = activityService.getPopularActivities(limit);
            return ApiResponse.success(activities);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @GetMapping("/{id}")
    public ApiResponse<Activity> getActivityById(@PathVariable Long id) {
        try {
            Activity activity = activityService.getActivityById(id);
            return ApiResponse.success(activity);
        } catch (Exception e) {
            return ApiResponse.error(404, e.getMessage());
        }
    }
    
    @PostMapping
    public ApiResponse<Activity> createActivity(@Valid @RequestBody ActivityRequest activityRequest) {
        try {
            Activity activity = activityService.createActivity(activityRequest);
            return ApiResponse.success("活动创建成功", activity);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @PutMapping("/{id}")
    public ApiResponse<Activity> updateActivity(@PathVariable Long id, @Valid @RequestBody ActivityRequest activityRequest) {
        try {
            Activity activity = activityService.updateActivity(id, activityRequest);
            return ApiResponse.success("活动更新成功", activity);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteActivity(@PathVariable Long id) {
        try {
            activityService.deleteActivity(id);
            return ApiResponse.success("活动删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @PostMapping("/{id}/join")
    public ApiResponse<Void> joinActivity(@PathVariable Long id) {
        try {
            activityService.joinActivity(id);
            return ApiResponse.success("报名成功", null);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @PostMapping("/{id}/leave")
    public ApiResponse<Void> leaveActivity(@PathVariable Long id) {
        try {
            activityService.leaveActivity(id);
            return ApiResponse.success("取消报名成功", null);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @GetMapping("/admin/all")
    public ApiResponse<Page<Activity>> getAllActivities(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Activity> activitiesPage = activityService.getAllActivities(pageable);
            return ApiResponse.success(activitiesPage);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @PostMapping("/admin/update-status")
    public ApiResponse<Void> updateActivityStatus() {
        try {
            activityService.updateActivityStatus();
            return ApiResponse.success("活动状态更新成功", null);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
}