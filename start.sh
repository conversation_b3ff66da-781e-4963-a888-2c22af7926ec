#!/bin/bash

echo "=== 校友信息管理系统启动脚本 ==="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 请先安装Java 8或以上版本"
    exit 1
fi

# 检查Maven
if ! command -v mvn &> /dev/null; then
    echo "错误: 请先安装Maven"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "错误: 请先安装Node.js"
    exit 1
fi

echo "环境检查完成..."

# 启动后端
echo "正在启动后端服务..."
cd backend
mvn clean install -q
if [ $? -eq 0 ]; then
    echo "后端编译成功，正在启动..."
    nohup mvn spring-boot:run > ../backend.log 2>&1 &
    BACKEND_PID=$!
    echo "后端服务已启动，PID: $BACKEND_PID"
    echo "后端日志: backend.log"
    
    # 等待后端启动
    sleep 10
    
    # 启动前端
    echo "正在启动前端服务..."
    cd ../frontend
    
    if [ ! -d "node_modules" ]; then
        echo "正在安装前端依赖..."
        npm install
    fi
    
    echo "前端正在启动，请稍候..."
    nohup npm run serve > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo "前端服务已启动，PID: $FRONTEND_PID"
    echo "前端日志: frontend.log"
    
    cd ..
    
    echo ""
    echo "=== 启动完成 ==="
    echo "后端地址: http://localhost:8080"
    echo "前端地址: http://localhost:3000"
    echo ""
    echo "后端进程 PID: $BACKEND_PID" 
    echo "前端进程 PID: $FRONTEND_PID"
    echo ""
    echo "要停止服务，请运行: ./stop.sh"
    
else
    echo "后端编译失败，请检查错误信息"
    exit 1
fi