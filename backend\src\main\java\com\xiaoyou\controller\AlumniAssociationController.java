package com.xiaoyou.controller;

import com.xiaoyou.dto.AlumniAssociationRequest;
import com.xiaoyou.dto.ApiResponse;
import com.xiaoyou.entity.AlumniAssociation;
import com.xiaoyou.service.AlumniAssociationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/alumni-associations")
@Validated
public class AlumniAssociationController {
    
    @Autowired
    private AlumniAssociationService alumniAssociationService;
    
    @GetMapping
    public ApiResponse<Page<AlumniAssociation>> getActiveAssociations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String address) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<AlumniAssociation> associationsPage;
            
            if (address != null && !address.trim().isEmpty()) {
                associationsPage = alumniAssociationService.searchByAddress(address, pageable);
            } else if (search != null && !search.trim().isEmpty()) {
                associationsPage = alumniAssociationService.searchAssociations(search, pageable);
            } else {
                associationsPage = alumniAssociationService.getAllActiveAssociations(pageable);
            }
            
            return ApiResponse.success(associationsPage);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @GetMapping("/{id}")
    public ApiResponse<AlumniAssociation> getAssociationById(@PathVariable Long id) {
        try {
            AlumniAssociation association = alumniAssociationService.getAssociationById(id);
            return ApiResponse.success(association);
        } catch (Exception e) {
            return ApiResponse.error(404, e.getMessage());
        }
    }
    
    @PostMapping
    public ApiResponse<AlumniAssociation> createAssociation(@Valid @RequestBody AlumniAssociationRequest request) {
        try {
            AlumniAssociation association = alumniAssociationService.createAssociation(request);
            return ApiResponse.success("校友会创建成功", association);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @PutMapping("/{id}")
    public ApiResponse<AlumniAssociation> updateAssociation(@PathVariable Long id, @Valid @RequestBody AlumniAssociationRequest request) {
        try {
            AlumniAssociation association = alumniAssociationService.updateAssociation(id, request);
            return ApiResponse.success("校友会更新成功", association);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteAssociation(@PathVariable Long id) {
        try {
            alumniAssociationService.deleteAssociation(id);
            return ApiResponse.success("校友会删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @PutMapping("/{id}/deactivate")
    public ApiResponse<Void> deactivateAssociation(@PathVariable Long id) {
        try {
            alumniAssociationService.deactivateAssociation(id);
            return ApiResponse.success("校友会已停用", null);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @PutMapping("/{id}/activate")
    public ApiResponse<Void> activateAssociation(@PathVariable Long id) {
        try {
            alumniAssociationService.activateAssociation(id);
            return ApiResponse.success("校友会已启用", null);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @GetMapping("/admin/all")
    public ApiResponse<Page<AlumniAssociation>> getAllAssociations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<AlumniAssociation> associationsPage = alumniAssociationService.getAllAssociations(pageable);
            return ApiResponse.success(associationsPage);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalAssociations", alumniAssociationService.getActiveAssociationCount());
            statistics.put("totalMembers", alumniAssociationService.getTotalMemberCount());
            
            List<AlumniAssociation> topAssociations = alumniAssociationService.getTopAssociationsByMemberCount(5);
            statistics.put("topAssociations", topAssociations);
            
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
}