<template>
  <Layout>
    <div class="alumni-association-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <i class="el-icon-user"></i>
            校友会
          </h1>
          <p class="page-description">连接校友，传承友谊，共创未来</p>
        </div>
      </div>

      <!-- 搜索和筛选区域 -->
      <div class="search-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索校友会名称"
              prefix-icon="el-icon-search"
              @keyup.enter.native="handleSearch"
              clearable
            />
          </el-col>
          <el-col :span="8">
            <el-input
              v-model="addressKeyword"
              placeholder="搜索地区"
              prefix-icon="el-icon-location"
              @keyup.enter.native="handleSearch"
              clearable
            />
          </el-col>
          <el-col :span="8">
            <el-button type="primary" @click="handleSearch" icon="el-icon-search">搜索</el-button>
            <el-button @click="resetSearch" icon="el-icon-refresh">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 统计信息 -->
      <div class="statistics-section" v-if="statistics">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-office-building"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.totalAssociations }}</div>
                <div class="stat-label">校友会总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-user"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.totalMembers }}</div>
                <div class="stat-label">校友总数</div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-trophy"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statistics.topAssociations ? statistics.topAssociations.length : 0 }}</div>
                <div class="stat-label">活跃校友会</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 校友会列表 -->
      <div class="associations-section">
        <div v-if="loading" class="loading-container">
          <el-loading-spinner></el-loading-spinner>
          <span>加载中...</span>
        </div>
        
        <div v-else-if="associations.length === 0" class="empty-container">
          <i class="el-icon-info"></i>
          <p>暂无校友会数据</p>
        </div>
        
        <div v-else class="associations-grid">
          <div 
            v-for="association in associations" 
            :key="association.id" 
            class="association-card"
            @click="viewAssociation(association.id)"
          >
            <div class="card-header">
              <div class="logo-container">
                <img 
                  v-if="association.logoUrl" 
                  :src="getLogoUrl(association.logoUrl)" 
                  :alt="association.name"
                  class="association-logo"
                  @error="handleLogoError"
                />
                <div v-else class="default-logo">
                  <i class="el-icon-office-building"></i>
                </div>
              </div>
              <div class="card-title">
                <h3>{{ association.name }}</h3>
                <p class="establishment-date" v-if="association.establishmentDate">
                  成立于 {{ formatDate(association.establishmentDate) }}
                </p>
              </div>
            </div>
            
            <div class="card-content">
              <p class="description">{{ association.description || '暂无描述' }}</p>
              
              <div class="info-row" v-if="association.president">
                <i class="el-icon-user-solid"></i>
                <span>会长：{{ association.president }}</span>
              </div>
              
              <div class="info-row" v-if="association.address">
                <i class="el-icon-location"></i>
                <span>{{ association.address }}</span>
              </div>
              
              <div class="info-row" v-if="association.memberCount">
                <i class="el-icon-user"></i>
                <span>成员：{{ association.memberCount }} 人</span>
              </div>
            </div>
            
            <div class="card-footer">
              <div class="contact-info">
                <span v-if="association.phone" class="contact-item">
                  <i class="el-icon-phone"></i>
                  {{ association.phone }}
                </span>
                <span v-if="association.email" class="contact-item">
                  <i class="el-icon-message"></i>
                  {{ association.email }}
                </span>
              </div>
              <el-button type="primary" size="small" @click.stop="viewAssociation(association.id)">
                查看详情
              </el-button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container" v-if="total > 0">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[6, 12, 18, 24]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/Layout.vue'
import axios from 'axios'

export default {
  name: 'AlumniAssociation',
  components: {
    Layout
  },
  data() {
    return {
      associations: [],
      statistics: null,
      loading: false,
      searchKeyword: '',
      addressKeyword: '',
      currentPage: 1,
      pageSize: 12,
      total: 0
    }
  },
  mounted() {
    this.fetchStatistics()
    this.fetchAssociations()
  },
  methods: {
    async fetchAssociations() {
      try {
        this.loading = true
        const params = {
          page: this.currentPage - 1,
          size: this.pageSize
        }
        
        if (this.searchKeyword) {
          params.search = this.searchKeyword
        }
        
        if (this.addressKeyword) {
          params.address = this.addressKeyword
        }
        
        const response = await axios.get('/api/alumni-associations', { params })
        
        if (response.data && response.data.code === 200) {
          const pageData = response.data.data
          this.associations = pageData.content || []
          this.total = pageData.totalElements || 0
        } else {
          this.$message.error('获取校友会数据失败')
        }
      } catch (error) {
        console.error('获取校友会数据失败:', error)
        this.$message.error('获取校友会数据失败')
      } finally {
        this.loading = false
      }
    },
    
    async fetchStatistics() {
      try {
        const response = await axios.get('/api/alumni-associations/statistics')
        if (response.data && response.data.code === 200) {
          this.statistics = response.data.data
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    },
    
    handleSearch() {
      this.currentPage = 1
      this.fetchAssociations()
    },
    
    resetSearch() {
      this.searchKeyword = ''
      this.addressKeyword = ''
      this.currentPage = 1
      this.fetchAssociations()
    },
    
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchAssociations()
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchAssociations()
    },
    
    viewAssociation(id) {
      this.$router.push(`/alumni-association/${id}`)
    },
    
    getLogoUrl(logoUrl) {
      if (!logoUrl) return ''
      if (logoUrl.startsWith('http')) return logoUrl
      if (logoUrl.startsWith('/uploads') || logoUrl.startsWith('/images')) {
        return `http://localhost:8080${logoUrl}`
      }
      return logoUrl
    },
    
    handleLogoError(event) {
      event.target.style.display = 'none'
      event.target.nextElementSibling.style.display = 'flex'
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.getFullYear() + '年'
    }
  }
}
</script>

<style scoped>
.alumni-association-container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 20px;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  text-align: center;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.page-description {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.statistics-section {
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  color: #6b7280;
  font-size: 14px;
}

.associations-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.loading-container,
.empty-container {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loading-container span,
.empty-container p {
  margin-top: 15px;
  font-size: 16px;
}

.empty-container i {
  font-size: 48px;
  color: #d1d5db;
}

.associations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.association-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.association-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-color: #667eea;
}

.card-header {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 15px;
}

.logo-container {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.association-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.default-logo {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 24px;
}

.card-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 5px 0;
  line-height: 1.3;
}

.establishment-date {
  color: #6b7280;
  font-size: 12px;
  margin: 0;
}

.card-content {
  margin-bottom: 15px;
}

.description {
  color: #4b5563;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #6b7280;
}

.info-row i {
  width: 14px;
  color: #9ca3af;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  border-top: 1px solid #f3f4f6;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #6b7280;
}

.contact-item i {
  width: 12px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alumni-association-container {
    padding: 10px;
  }
  
  .page-header {
    padding: 30px 15px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .associations-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .association-card {
    padding: 15px;
  }
  
  .search-section .el-col {
    margin-bottom: 10px;
  }
}
</style>