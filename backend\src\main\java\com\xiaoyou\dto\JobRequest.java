package com.xiaoyou.dto;

import com.xiaoyou.entity.Job;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

public class JobRequest {
    
    @NotBlank(message = "职位标题不能为空")
    @Size(max = 200, message = "职位标题长度不能超过200个字符")
    private String title;
    
    @NotBlank(message = "职位描述不能为空")
    private String description;
    
    @Size(max = 500, message = "职位摘要长度不能超过500个字符")
    private String summary;
    
    @NotBlank(message = "公司名称不能为空")
    @Size(max = 100, message = "公司名称长度不能超过100个字符")
    private String company;
    
    @Size(max = 100, message = "工作地点长度不能超过100个字符")
    private String location;
    
    @Size(max = 100, message = "薪资范围长度不能超过100个字符")
    private String salary;
    
    @Size(max = 100, message = "工作经验要求长度不能超过100个字符")
    private String experience;
    
    @Size(max = 100, message = "学历要求长度不能超过100个字符")
    private String education;
    
    @Size(max = 255, message = "图片URL长度不能超过255个字符")
    private String imageUrl;
    
    @Email(message = "联系邮箱格式不正确")
    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    private String contactEmail;
    
    @Size(max = 50, message = "联系电话长度不能超过50个字符")
    private String contactPhone;
    
    @Size(max = 100, message = "联系人长度不能超过100个字符")
    private String contactPerson;
    
    private Job.JobType type;
    
    private Job.JobStatus status;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deadline;
    
    private Boolean published;
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
    
    public String getCompany() {
        return company;
    }
    
    public void setCompany(String company) {
        this.company = company;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public String getSalary() {
        return salary;
    }
    
    public void setSalary(String salary) {
        this.salary = salary;
    }
    
    public String getExperience() {
        return experience;
    }
    
    public void setExperience(String experience) {
        this.experience = experience;
    }
    
    public String getEducation() {
        return education;
    }
    
    public void setEducation(String education) {
        this.education = education;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getContactEmail() {
        return contactEmail;
    }
    
    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }
    
    public String getContactPhone() {
        return contactPhone;
    }
    
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }
    
    public String getContactPerson() {
        return contactPerson;
    }
    
    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }
    
    public Job.JobType getType() {
        return type;
    }
    
    public void setType(Job.JobType type) {
        this.type = type;
    }
    
    public Job.JobStatus getStatus() {
        return status;
    }
    
    public void setStatus(Job.JobStatus status) {
        this.status = status;
    }
    
    public LocalDateTime getDeadline() {
        return deadline;
    }
    
    public void setDeadline(LocalDateTime deadline) {
        this.deadline = deadline;
    }
    
    public Boolean getPublished() {
        return published;
    }
    
    public void setPublished(Boolean published) {
        this.published = published;
    }
}