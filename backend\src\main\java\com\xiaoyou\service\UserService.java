package com.xiaoyou.service;

import com.xiaoyou.dto.LoginRequest;
import com.xiaoyou.dto.RegisterRequest;
import com.xiaoyou.entity.User;
import com.xiaoyou.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    public User register(RegisterRequest registerRequest) {
        if (userRepository.existsByUsername(registerRequest.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        if (userRepository.existsByEmail(registerRequest.getEmail())) {
            throw new RuntimeException("邮箱已被注册");
        }
        
        User user = new User();
        user.setUsername(registerRequest.getUsername());
        user.setPassword(registerRequest.getPassword());
        user.setEmail(registerRequest.getEmail());
        user.setPhone(registerRequest.getPhone());
        user.setRealName(registerRequest.getRealName());
        user.setSchool(registerRequest.getSchool());
        user.setMajor(registerRequest.getMajor());
        user.setGraduationYear(registerRequest.getGraduationYear());
        user.setCompany(registerRequest.getCompany());
        user.setPosition(registerRequest.getPosition());
        user.setRole(User.Role.USER); // 默认角色为普通用户
        
        return userRepository.save(user);
    }
    
    public User login(LoginRequest loginRequest) {
        User user = userRepository.findByUsername(loginRequest.getUsername())
                .orElseThrow(() -> new RuntimeException("用户名或密码错误"));
        
        if (!user.getPassword().equals(loginRequest.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        return user;
    }
    
    public User getUserById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
    }
    
    public User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
    }
    
    public User adminLogin(LoginRequest loginRequest) {
        User user = userRepository.findByUsername(loginRequest.getUsername())
                .orElseThrow(() -> new RuntimeException("管理员账号或密码错误"));
        
        if (!user.getPassword().equals(loginRequest.getPassword())) {
            throw new RuntimeException("管理员账号或密码错误");
        }
        
        if (!"ADMIN".equals(user.getRole())) {
            throw new RuntimeException("您没有管理员权限");
        }
        
        return user;
    }
    
    /**
     * 根据ID查找用户
     */
    public User findById(Long id) {
        return userRepository.findById(id).orElse(null);
    }
    
    /**
     * 保存用户
     */
    public User save(User user) {
        return userRepository.save(user);
    }
    
    /**
     * 验证密码
     */
    public boolean verifyPassword(User user, String rawPassword) {
        // 这里应该使用密码加密验证，暂时使用简单比较
        // 实际项目中应该使用BCrypt等加密方式
        return user.getPassword().equals(rawPassword);
    }
    
    /**
     * 加密密码
     */
    public String encodePassword(String rawPassword) {
        // 这里应该使用BCrypt等加密方式
        // 暂时直接返回原密码，实际项目中需要加密
        return rawPassword;
    }
    
    /**
     * 获取用户统计信息
     */
    public Map<String, Object> getUserStats(Long userId) {
        User user = findById(userId);
        if (user == null) {
            return new HashMap<>();
        }
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("favoriteCount", user.getFavoriteCount() != null ? user.getFavoriteCount() : 0);
        stats.put("postCount", user.getPostCount() != null ? user.getPostCount() : 0);
        stats.put("followCount", user.getFollowCount() != null ? user.getFollowCount() : 0);
        
        return stats;
    }
    
    /**
     * 更新用户收藏数量
     */
    public void updateFavoriteCount(Long userId, int increment) {
        User user = findById(userId);
        if (user != null) {
            int currentCount = user.getFavoriteCount() != null ? user.getFavoriteCount() : 0;
            user.setFavoriteCount(Math.max(0, currentCount + increment));
            save(user);
        }
    }
}
