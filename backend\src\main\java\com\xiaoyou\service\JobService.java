    package com.xiaoyou.service;

import com.xiaoyou.dto.JobRequest;
import com.xiaoyou.entity.Job;
import com.xiaoyou.repository.JobRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class JobService {
    
    @Autowired
    private JobRepository jobRepository;
    
    public Page<Job> getAllPublishedJobs(Pageable pageable) {
        return jobRepository.findByPublishedTrueOrderByPublishTimeDesc(pageable);
    }
    
    public Page<Job> searchJobs(String keyword, Pageable pageable) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllPublishedJobs(pageable);
        }
        return jobRepository.searchJobs(keyword, pageable);
    }
    
    public Page<Job> getJobsByType(Job.JobType type, Pageable pageable) {
        return jobRepository.findByTypeAndPublishedTrueOrderByPublishTimeDesc(type, pageable);
    }
    
    public Page<Job> getJobsByStatus(Job.JobStatus status, Pageable pageable) {
        return jobRepository.findByStatusAndPublishedTrueOrderByPublishTimeDesc(status, pageable);
    }
    
    public Page<Job> getJobsByLocation(String location, Pageable pageable) {
        return jobRepository.findByLocationContainingIgnoreCaseAndPublishedTrueOrderByPublishTimeDesc(location, pageable);
    }
    
    public Page<Job> getJobsByCompany(String company, Pageable pageable) {
        return jobRepository.findByCompanyContainingIgnoreCaseAndPublishedTrueOrderByPublishTimeDesc(company, pageable);
    }
    
    @Transactional
    public Job getJobById(Long id) {
        Job job = jobRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("招聘信息不存在"));
        jobRepository.incrementViewCount(id);
        return job;
    }
    
    public Job createJob(JobRequest jobRequest) {
        Job job = new Job();
        updateJobFromRequest(job, jobRequest);
        return jobRepository.save(job);
    }
    
    public Job updateJob(Long id, JobRequest jobRequest) {
        Job job = jobRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("招聘信息不存在"));
        updateJobFromRequest(job, jobRequest);
        return jobRepository.save(job);
    }
    
    private void updateJobFromRequest(Job job, JobRequest jobRequest) {
        job.setTitle(jobRequest.getTitle());
        job.setDescription(jobRequest.getDescription());
        job.setSummary(jobRequest.getSummary());
        job.setCompany(jobRequest.getCompany());
        job.setLocation(jobRequest.getLocation());
        job.setSalary(jobRequest.getSalary());
        job.setExperience(jobRequest.getExperience());
        job.setEducation(jobRequest.getEducation());
        job.setImageUrl(jobRequest.getImageUrl());
        job.setContactEmail(jobRequest.getContactEmail());
        job.setContactPhone(jobRequest.getContactPhone());
        job.setContactPerson(jobRequest.getContactPerson());
        job.setType(jobRequest.getType());
        job.setStatus(jobRequest.getStatus());
        job.setDeadline(jobRequest.getDeadline());
        job.setPublished(jobRequest.getPublished());
    }
    
    public void deleteJob(Long id) {
        if (!jobRepository.existsById(id)) {
            throw new RuntimeException("招聘信息不存在");
        }
        jobRepository.deleteById(id);
    }
    
    public Page<Job> getAllJobs(Pageable pageable) {
        return jobRepository.findAll(pageable);
    }
    
    public Page<Job> getAllJobsForAdmin(Pageable pageable, String search, Boolean published) {
        if (search != null && !search.trim().isEmpty()) {
            if (published != null) {
                return jobRepository.findByTitleContainingIgnoreCaseAndPublishedOrderByCreateTimeDesc(search, published, pageable);
            } else {
                return jobRepository.findByTitleContainingIgnoreCaseOrderByCreateTimeDesc(search, pageable);
            }
        } else if (published != null) {
            return jobRepository.findByPublishedOrderByCreateTimeDesc(published, pageable);
        } else {
            return jobRepository.findAllByOrderByCreateTimeDesc(pageable);
        }
    }
    
    @Transactional
    public Job togglePublishStatus(Long id) {
        Job job = jobRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("招聘信息不存在"));
        job.setPublished(!job.getPublished());
        return jobRepository.save(job);
    }
}
