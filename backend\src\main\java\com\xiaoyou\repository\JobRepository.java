package com.xiaoyou.repository;

import com.xiaoyou.entity.Job;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface JobRepository extends JpaRepository<Job, Long> {
    
    Page<Job> findByPublishedTrueOrderByPublishTimeDesc(Pageable pageable);
    
    Page<Job> findByTitleContainingIgnoreCaseAndPublishedTrueOrderByPublishTimeDesc(String title, Pageable pageable);
    
    Page<Job> findByCompanyContainingIgnoreCaseAndPublishedTrueOrderByPublishTimeDesc(String company, Pageable pageable);
    
    Page<Job> findByLocationContainingIgnoreCaseAndPublishedTrueOrderByPublishTimeDesc(String location, Pageable pageable);
    
    Page<Job> findByTypeAndPublishedTrueOrderByPublishTimeDesc(Job.JobType type, Pageable pageable);
    
    Page<Job> findByStatusAndPublishedTrueOrderByPublishTimeDesc(Job.JobStatus status, Pageable pageable);
    
    @Query("SELECT j FROM Job j WHERE j.published = true AND " +
           "(j.title LIKE %:keyword% OR j.company LIKE %:keyword% OR j.location LIKE %:keyword%) " +
           "ORDER BY j.publishTime DESC")
    Page<Job> searchJobs(@Param("keyword") String keyword, Pageable pageable);
    
    @Modifying
    @Query("UPDATE Job j SET j.viewCount = j.viewCount + 1 WHERE j.id = :id")
    int incrementViewCount(@Param("id") Long id);
    
    // 管理员页面查询方法
    Page<Job> findAllByOrderByCreateTimeDesc(Pageable pageable);
    
    Page<Job> findByPublishedOrderByCreateTimeDesc(Boolean published, Pageable pageable);
    
    Page<Job> findByTitleContainingIgnoreCaseOrderByCreateTimeDesc(String title, Pageable pageable);
    
    Page<Job> findByTitleContainingIgnoreCaseAndPublishedOrderByCreateTimeDesc(String title, Boolean published, Pageable pageable);
}