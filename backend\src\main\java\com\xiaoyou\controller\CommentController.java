package com.xiaoyou.controller;

import com.xiaoyou.dto.ApiResponse;
import com.xiaoyou.dto.CommentRequest;
import com.xiaoyou.entity.Comment;
import com.xiaoyou.entity.User;
import com.xiaoyou.service.CommentService;
import com.xiaoyou.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/comments")
@Validated
@CrossOrigin(origins = "http://localhost:8081")
public class CommentController {
    
    @Autowired
    private CommentService commentService;
    
    @Autowired
    private UserService userService;
    
    // 获取帖子的评论
    @GetMapping("/post/{postId}")
    public ApiResponse<List<Comment>> getCommentsByPostId(@PathVariable Long postId) {
        try {
            List<Comment> comments = commentService.getCommentsByPostId(postId);
            return ApiResponse.success("获取评论成功", comments);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取评论失败: " + e.getMessage());
        }
    }
    
    // 获取评论的回复
    @GetMapping("/{parentId}/replies")
    public ApiResponse<List<Comment>> getRepliesByParentId(@PathVariable Long parentId) {
        try {
            List<Comment> replies = commentService.getRepliesByParentId(parentId);
            return ApiResponse.success("获取回复成功", replies);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取回复失败: " + e.getMessage());
        }
    }
    
    // 创建评论
    @PostMapping
    public ApiResponse<Comment> createComment(@Valid @RequestBody CommentRequest commentRequest, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.error(401, "请先登录");
            }
            
            Comment comment = commentService.createComment(commentRequest, userId);
            return ApiResponse.success("评论创建成功", comment);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    // 更新评论
    @PutMapping("/{id}")
    public ApiResponse<Comment> updateComment(@PathVariable Long id, @Valid @RequestBody CommentRequest commentRequest, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.error(401, "请先登录");
            }
            
            Comment comment = commentService.updateComment(id, commentRequest, userId);
            return ApiResponse.success("评论更新成功", comment);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    // 删除评论
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteComment(@PathVariable Long id, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.error(401, "请先登录");
            }
            
            commentService.deleteComment(id, userId);
            return ApiResponse.success("评论删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    // 获取用户的评论
    @GetMapping("/user/{userId}")
    public ApiResponse<Page<Comment>> getUserComments(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Comment> comments = commentService.getUserComments(userId, pageable);
            return ApiResponse.success("获取用户评论成功", comments);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取用户评论失败: " + e.getMessage());
        }
    }
    
    // 管理员接口 - 获取所有评论
    @GetMapping("/admin/all")
    public ApiResponse<Page<Comment>> getAllCommentsForAdmin(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            HttpServletRequest request) {
        try {
            if (!isAdmin(request)) {
                return ApiResponse.error(403, "权限不足");
            }
            
            Pageable pageable = PageRequest.of(page, size);
            Page<Comment> comments;
            
            if (status != null && !status.trim().isEmpty()) {
                comments = commentService.getCommentsByStatus(status.toUpperCase(), pageable);
            } else {
                comments = commentService.getAllCommentsForAdmin(pageable);
            }
            
            return ApiResponse.success("获取评论列表成功", comments);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取评论列表失败: " + e.getMessage());
        }
    }
    
    // 管理员接口 - 更改评论状态
    @PutMapping("/admin/{id}/status")
    public ApiResponse<Comment> updateCommentStatus(@PathVariable Long id, @RequestParam String status, HttpServletRequest request) {
        try {
            if (!isAdmin(request)) {
                return ApiResponse.error(403, "权限不足");
            }
            
            Comment comment = commentService.updateCommentStatus(id, status.toUpperCase());
            return ApiResponse.success("更新评论状态成功", comment);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    // 辅助方法：获取当前用户ID
    private Long getCurrentUserId(HttpServletRequest request) {
        String userIdHeader = request.getHeader("User-Id");
        if (userIdHeader != null) {
            try {
                return Long.parseLong(userIdHeader);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    // 辅助方法：检查是否为管理员
    private boolean isAdmin(HttpServletRequest request) {
        Long userId = getCurrentUserId(request);
        if (userId == null) {
            return false;
        }
        
        try {
            User user = userService.getUserById(userId);
            return user.getRole() == User.Role.ADMIN;
        } catch (Exception e) {
            return false;
        }
    }
}