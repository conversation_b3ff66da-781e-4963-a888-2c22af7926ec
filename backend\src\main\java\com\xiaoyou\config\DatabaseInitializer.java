package com.xiaoyou.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
public class DatabaseInitializer implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        createFavoritesTable();
        createAlumniAssociationsTable();
    }

    private void createFavoritesTable() {
        try {
            String sql = "CREATE TABLE IF NOT EXISTS favorites (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "user_id BIGINT NOT NULL, " +
                    "item_type VARCHAR(50) NOT NULL COMMENT '收藏项目类型：news, activity等', " +
                    "item_id BIGINT NOT NULL COMMENT '收藏项目ID', " +
                    "create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间', " +
                    "INDEX idx_user_id (user_id), " +
                    "INDEX idx_item_type_id (item_type, item_id), " +
                    "INDEX idx_user_type (user_id, item_type), " +
                    "UNIQUE KEY uk_user_item (user_id, item_type, item_id)" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表'";
            
            jdbcTemplate.execute(sql);
            System.out.println("Favorites table created successfully");
        } catch (Exception e) {
            System.err.println("Error creating favorites table: " + e.getMessage());
        }
    }

    private void createAlumniAssociationsTable() {
        try {
            String sql = "CREATE TABLE IF NOT EXISTS alumni_associations (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(200) NOT NULL COMMENT '校友会名称', " +
                    "description TEXT COMMENT '校友会描述', " +
                    "president VARCHAR(100) COMMENT '会长姓名', " +
                    "phone VARCHAR(20) COMMENT '联系电话', " +
                    "email VARCHAR(100) COMMENT '联系邮箱', " +
                    "address VARCHAR(255) COMMENT '地址', " +
                    "logo_url VARCHAR(255) COMMENT 'Logo图片URL', " +
                    "establishment_date DATETIME COMMENT '成立时间', " +
                    "member_count INT DEFAULT 0 COMMENT '成员数量', " +
                    "activities TEXT COMMENT '活动介绍', " +
                    "achievements TEXT COMMENT '成就介绍', " +
                    "active BOOLEAN DEFAULT TRUE COMMENT '是否活跃', " +
                    "create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', " +
                    "update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', " +
                    "INDEX idx_name (name), " +
                    "INDEX idx_active (active), " +
                    "INDEX idx_address (address), " +
                    "INDEX idx_create_time (create_time)" +
                    ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='校友会表'";
            
            jdbcTemplate.execute(sql);
            System.out.println("Alumni associations table created successfully");
        } catch (Exception e) {
            System.err.println("Error creating alumni associations table: " + e.getMessage());
        }
    }
}