package com.xiaoyou.controller;

import com.xiaoyou.dto.ApiResponse;
import com.xiaoyou.dto.PostRequest;
import com.xiaoyou.entity.Post;
import com.xiaoyou.entity.User;
import com.xiaoyou.service.PostService;
import com.xiaoyou.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/posts")
@Validated
@CrossOrigin(origins = "http://localhost:8081")
public class PostController {
    
    @Autowired
    private PostService postService;
    
    @Autowired
    private UserService userService;
    
    // 获取帖子列表
    @GetMapping
    public ApiResponse<Page<Post>> getPosts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) boolean featured) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Post> posts;
            
            if (featured) {
                posts = postService.getFeaturedPosts(pageable);
            } else if (search != null && !search.trim().isEmpty()) {
                posts = postService.searchPosts(search, pageable);
            } else if (category != null && !category.trim().isEmpty()) {
                posts = postService.getPostsByCategory(category, pageable);
            } else {
                posts = postService.getPublishedPosts(pageable);
            }
            
            return ApiResponse.success("获取帖子列表成功", posts);
        } catch (Exception e) {
            e.printStackTrace(); // 添加详细错误日志
            return ApiResponse.error(500, "获取帖子列表失败: " + e.getMessage());
        }
    }
    
    // 获取置顶帖子
    @GetMapping("/pinned")
    public ApiResponse<List<Post>> getPinnedPosts() {
        try {
            List<Post> pinnedPosts = postService.getPinnedPosts();
            return ApiResponse.success("获取置顶帖子成功", pinnedPosts);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取置顶帖子失败: " + e.getMessage());
        }
    }
    
    // 获取帖子详情
    @GetMapping("/{id}")
    public ApiResponse<Post> getPostById(@PathVariable Long id) {
        try {
            Post post = postService.getPostById(id);
            return ApiResponse.success("获取帖子详情成功", post);
        } catch (Exception e) {
            return ApiResponse.error(404, e.getMessage());
        }
    }
    
    // 创建帖子
    @PostMapping
    public ApiResponse<Post> createPost(@Valid @RequestBody PostRequest postRequest, HttpServletRequest request) {
        try {
            // 从请求中获取当前用户ID（这里需要根据你的认证机制调整）
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.error(401, "请先登录");
            }
            
            Post post = postService.createPost(postRequest, userId);
            return ApiResponse.success("帖子创建成功", post);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    // 更新帖子
    @PutMapping("/{id}")
    public ApiResponse<Post> updatePost(@PathVariable Long id, @Valid @RequestBody PostRequest postRequest, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.error(401, "请先登录");
            }
            
            Post post = postService.updatePost(id, postRequest, userId);
            return ApiResponse.success("帖子更新成功", post);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    // 删除帖子
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deletePost(@PathVariable Long id, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.error(401, "请先登录");
            }
            
            postService.deletePost(id, userId);
            return ApiResponse.success("帖子删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    // 获取用户的帖子
    @GetMapping("/user/{userId}")
    public ApiResponse<Page<Post>> getUserPosts(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Post> posts = postService.getUserPosts(userId, pageable);
            return ApiResponse.success("获取用户帖子成功", posts);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取用户帖子失败: " + e.getMessage());
        }
    }
    
    // 管理员接口 - 获取论坛统计数据
    @GetMapping("/admin/stats")
    public ApiResponse<Map<String, Object>> getForumStats(HttpServletRequest request) {
        try {
            if (!isAdmin(request)) {
                return ApiResponse.error(403, "权限不足");
            }
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalPosts", postService.getTotalPosts());
            stats.put("totalComments", postService.getTotalComments());
            stats.put("totalLikes", postService.getTotalLikes());
            stats.put("todayPosts", postService.getTodayPosts());
            
            return ApiResponse.success("获取统计数据成功", stats);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取统计数据失败: " + e.getMessage());
        }
    }
    
    // 管理员接口 - 获取所有帖子
    @GetMapping("/admin/all")
    public ApiResponse<Page<Post>> getAllPostsForAdmin(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            HttpServletRequest request) {
        try {
            // 检查管理员权限
            if (!isAdmin(request)) {
                return ApiResponse.error(403, "权限不足");
            }
            
            Pageable pageable = PageRequest.of(page, size);
            Page<Post> posts;
            
            if (status != null && !status.trim().isEmpty()) {
                posts = postService.getPostsByStatus(status.toUpperCase(), pageable);
            } else {
                posts = postService.getAllPostsForAdmin(pageable);
            }
            
            return ApiResponse.success("获取帖子列表成功", posts);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取帖子列表失败: " + e.getMessage());
        }
    }
    
    // 管理员接口 - 切换置顶状态
    @PutMapping("/admin/{id}/toggle-pin")
    public ApiResponse<Post> togglePinStatus(@PathVariable Long id, HttpServletRequest request) {
        try {
            if (!isAdmin(request)) {
                return ApiResponse.error(403, "权限不足");
            }
            
            Post post = postService.togglePinStatus(id);
            return ApiResponse.success("切换置顶状态成功", post);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    // 管理员接口 - 切换精华状态
    @PutMapping("/admin/{id}/toggle-featured")
    public ApiResponse<Post> toggleFeaturedStatus(@PathVariable Long id, HttpServletRequest request) {
        try {
            if (!isAdmin(request)) {
                return ApiResponse.error(403, "权限不足");
            }
            
            Post post = postService.toggleFeaturedStatus(id);
            return ApiResponse.success("切换精华状态成功", post);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    // 管理员接口 - 更改帖子状态
    @PutMapping("/admin/{id}/status")
    public ApiResponse<Post> updatePostStatus(@PathVariable Long id, @RequestParam String status, HttpServletRequest request) {
        try {
            if (!isAdmin(request)) {
                return ApiResponse.error(403, "权限不足");
            }
            
            Post post = postService.updatePostStatus(id, status.toUpperCase());
            return ApiResponse.success("更新帖子状态成功", post);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    // 增加帖子浏览量
    @PostMapping("/{id}/view")
    public ApiResponse<Void> incrementViewCount(@PathVariable Long id) {
        try {
            postService.incrementViewCount(id);
            return ApiResponse.success("浏览量增加成功", null);
        } catch (Exception e) {
            return ApiResponse.error(500, "增加浏览量失败: " + e.getMessage());
        }
    }
    
    // 辅助方法：获取当前用户ID
    private Long getCurrentUserId(HttpServletRequest request) {
        // 这里需要根据你的认证机制来实现
        // 例如从JWT token中解析用户ID，或从session中获取
        String userIdHeader = request.getHeader("User-Id");
        if (userIdHeader != null) {
            try {
                return Long.parseLong(userIdHeader);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    // 辅助方法：检查是否为管理员
    private boolean isAdmin(HttpServletRequest request) {
        Long userId = getCurrentUserId(request);
        System.out.println("管理员权限检查 - 用户ID: " + userId);
        
        if (userId == null) {
            System.out.println("管理员权限检查失败 - 用户ID为空");
            return false;
        }
        
        try {
            User user = userService.getUserById(userId);
            System.out.println("管理员权限检查 - 用户: " + user.getUsername() + ", 角色: " + user.getRole());
            boolean isAdmin = user.getRole() == User.Role.ADMIN;
            System.out.println("管理员权限检查结果: " + isAdmin);
            return isAdmin;
        } catch (Exception e) {
            System.out.println("管理员权限检查异常: " + e.getMessage());
            return false;
        }
    }
}