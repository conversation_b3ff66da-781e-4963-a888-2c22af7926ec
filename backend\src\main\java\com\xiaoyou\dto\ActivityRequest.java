package com.xiaoyou.dto;

import com.xiaoyou.entity.Activity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import java.time.LocalDateTime;

public class ActivityRequest {
    
    @NotBlank(message = "活动标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    private String title;
    
    @NotBlank(message = "活动描述不能为空")
    private String description;
    
    @Size(max = 500, message = "摘要长度不能超过500个字符")
    private String summary;
    
    @Size(max = 100, message = "组织者长度不能超过100个字符")
    private String organizer;
    
    @Size(max = 255, message = "图片URL长度不能超过255个字符")
    private String imageUrl;
    
    @NotBlank(message = "活动地点不能为空")
    @Size(max = 255, message = "地点长度不能超过255个字符")
    private String location;
    
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;
    
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;
    
    @Min(value = 1, message = "最大参与人数必须大于0")
    private Integer maxParticipants;
    
    @Min(value = 0, message = "费用不能为负数")
    private Double fee;
    
    private Activity.ActivityStatus status;
    
    private Activity.ActivityType type;
    
    private Boolean published;
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
    
    public String getOrganizer() {
        return organizer;
    }
    
    public void setOrganizer(String organizer) {
        this.organizer = organizer;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public Integer getMaxParticipants() {
        return maxParticipants;
    }
    
    public void setMaxParticipants(Integer maxParticipants) {
        this.maxParticipants = maxParticipants;
    }
    
    public Double getFee() {
        return fee;
    }
    
    public void setFee(Double fee) {
        this.fee = fee;
    }
    
    public Activity.ActivityStatus getStatus() {
        return status;
    }
    
    public void setStatus(Activity.ActivityStatus status) {
        this.status = status;
    }
    
    public Activity.ActivityType getType() {
        return type;
    }
    
    public void setType(Activity.ActivityType type) {
        this.type = type;
    }
    
    public Boolean getPublished() {
        return published;
    }
    
    public void setPublished(Boolean published) {
        this.published = published;
    }
}