package com.xiaoyou.config;

import com.xiaoyou.entity.Post;
import com.xiaoyou.entity.User;
import com.xiaoyou.repository.PostRepository;
import com.xiaoyou.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class DataInitializer implements CommandLineRunner {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PostRepository postRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public void run(String... args) throws Exception {
        System.out.println("=== DataInitializer 开始执行 ===");
        
        // 检查是否已有数据
        long userCount = userRepository.count();
        long postCount = postRepository.count();
        
        System.out.println("当前用户数量: " + userCount);
        System.out.println("当前帖子数量: " + postCount);
        
        if (userCount == 0) {
            System.out.println("开始初始化用户数据...");
            initializeUsers();
        } else {
            System.out.println("用户数据已存在，跳过初始化");
        }
        
        if (postCount == 0) {
            System.out.println("开始初始化帖子数据...");
            initializePosts();
        } else {
            System.out.println("帖子数据已存在，跳过初始化");
        }
        
        System.out.println("=== DataInitializer 执行完成 ===");
    }
    
    private void initializeUsers() {
        try {
            System.out.println("正在创建管理员用户...");
            // 创建管理员用户
            User admin = new User();
            admin.setUsername("admin");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setEmail("<EMAIL>");
            admin.setRealName("系统管理员");
            admin.setRole(User.Role.ADMIN);
            admin.setCreateTime(LocalDateTime.now());
            admin.setUpdateTime(LocalDateTime.now());
            User savedAdmin = userRepository.save(admin);
            System.out.println("管理员用户创建成功，ID: " + savedAdmin.getId());
            
            System.out.println("正在创建测试用户...");
            // 创建测试用户
            User testUser = new User();
            testUser.setUsername("testuser");
            testUser.setPassword(passwordEncoder.encode("test123"));
            testUser.setEmail("<EMAIL>");
            testUser.setRealName("测试用户");
            testUser.setSchool("测试大学");
            testUser.setMajor("计算机科学");
            testUser.setGraduationYear(2020);
            testUser.setRole(User.Role.USER);
            testUser.setCreateTime(LocalDateTime.now());
            testUser.setUpdateTime(LocalDateTime.now());
            User savedTestUser = userRepository.save(testUser);
            System.out.println("测试用户创建成功，ID: " + savedTestUser.getId());
            
            System.out.println("✅ 初始化用户数据完成，共创建 " + userRepository.count() + " 个用户");
        } catch (Exception e) {
            System.err.println("❌ 初始化用户数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void initializePosts() {
        try {
            System.out.println("正在查找用户...");
            User admin = userRepository.findByUsername("admin").orElse(null);
            User testUser = userRepository.findByUsername("testuser").orElse(null);
            
            if (admin == null) {
                System.err.println("❌ 无法找到管理员用户，跳过帖子初始化");
                return;
            }
            if (testUser == null) {
                System.err.println("❌ 无法找到测试用户，跳过帖子初始化");
                return;
            }
            
            System.out.println("找到用户 - 管理员ID: " + admin.getId() + ", 测试用户ID: " + testUser.getId());
            
            System.out.println("正在创建置顶帖子...");
            // 创建置顶帖子
            Post pinnedPost = new Post();
            pinnedPost.setTitle("欢迎来到校友论坛！");
            pinnedPost.setContent("这里是校友们交流分享的地方，欢迎大家积极参与讨论。请遵守论坛规则，文明发言。");
            pinnedPost.setSummary("论坛欢迎帖，介绍论坛使用规则");
            pinnedPost.setCategory("ANNOUNCEMENT");
            pinnedPost.setStatus("PUBLISHED");
            pinnedPost.setAuthor(admin);
            pinnedPost.setIsPinned(true);
            pinnedPost.setIsFeatured(true);
            pinnedPost.setPublished(true);
            pinnedPost.setViewCount(100);
            pinnedPost.setLikeCount(15);
            pinnedPost.setCommentCount(5);
            pinnedPost.setCreateTime(LocalDateTime.now().minusDays(7));
            pinnedPost.setPublishTime(LocalDateTime.now().minusDays(7));
            Post savedPinnedPost = postRepository.save(pinnedPost);
            System.out.println("置顶帖子创建成功，ID: " + savedPinnedPost.getId());
            
            System.out.println("正在创建普通帖子1...");
            // 创建普通帖子
            Post normalPost1 = new Post();
            normalPost1.setTitle("分享一下我的求职经验");
            normalPost1.setContent("最近刚找到工作，想和大家分享一下求职过程中的经验和心得。希望对正在找工作的同学有所帮助。");
            normalPost1.setSummary("求职经验分享");
            normalPost1.setCategory("CAREER");
            normalPost1.setStatus("PUBLISHED");
            normalPost1.setAuthor(testUser);
            normalPost1.setIsPinned(false);
            normalPost1.setIsFeatured(false);
            normalPost1.setPublished(true);
            normalPost1.setViewCount(50);
            normalPost1.setLikeCount(8);
            normalPost1.setCommentCount(3);
            normalPost1.setCreateTime(LocalDateTime.now().minusDays(3));
            normalPost1.setPublishTime(LocalDateTime.now().minusDays(3));
            Post savedNormalPost1 = postRepository.save(normalPost1);
            System.out.println("普通帖子1创建成功，ID: " + savedNormalPost1.getId());
            
            System.out.println("正在创建普通帖子2...");
            Post normalPost2 = new Post();
            normalPost2.setTitle("校友聚会活动通知");
            normalPost2.setContent("计划在下个月组织一次校友聚会，地点在市中心，欢迎大家报名参加。详细信息请关注后续通知。");
            normalPost2.setSummary("校友聚会活动通知");
            normalPost2.setCategory("ALUMNI");
            normalPost2.setStatus("PUBLISHED");
            normalPost2.setAuthor(admin);
            normalPost2.setIsPinned(false);
            normalPost2.setIsFeatured(true);
            normalPost2.setPublished(true);
            normalPost2.setViewCount(75);
            normalPost2.setLikeCount(12);
            normalPost2.setCommentCount(7);
            normalPost2.setCreateTime(LocalDateTime.now().minusDays(1));
            normalPost2.setPublishTime(LocalDateTime.now().minusDays(1));
            Post savedNormalPost2 = postRepository.save(normalPost2);
            System.out.println("普通帖子2创建成功，ID: " + savedNormalPost2.getId());
            
            System.out.println("✅ 初始化帖子数据完成，共创建 " + postRepository.count() + " 个帖子");
        } catch (Exception e) {
            System.err.println("❌ 初始化帖子数据失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}