package com.xiaoyou.service;

import com.xiaoyou.dto.AlumniAssociationRequest;
import com.xiaoyou.entity.AlumniAssociation;
import com.xiaoyou.repository.AlumniAssociationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class AlumniAssociationService {
    
    @Autowired
    private AlumniAssociationRepository alumniAssociationRepository;
    
    public Page<AlumniAssociation> getAllActiveAssociations(Pageable pageable) {
        return alumniAssociationRepository.findByActiveTrueOrderByCreateTimeDesc(pageable);
    }
    
    public Page<AlumniAssociation> searchAssociations(String keyword, Pageable pageable) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllActiveAssociations(pageable);
        }
        return alumniAssociationRepository.findByNameContainingIgnoreCaseAndActiveTrueOrderByCreateTimeDesc(keyword, pageable);
    }
    
    public Page<AlumniAssociation> searchByAddress(String address, Pageable pageable) {
        if (address == null || address.trim().isEmpty()) {
            return getAllActiveAssociations(pageable);
        }
        return alumniAssociationRepository.findByAddressContainingIgnoreCaseAndActiveTrueOrderByCreateTimeDesc(address, pageable);
    }
    
    public AlumniAssociation getAssociationById(Long id) {
        return alumniAssociationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("校友会不存在"));
    }
    
    public AlumniAssociation createAssociation(AlumniAssociationRequest request) {
        AlumniAssociation association = new AlumniAssociation();
        copyRequestToEntity(request, association);
        return alumniAssociationRepository.save(association);
    }
    
    public AlumniAssociation updateAssociation(Long id, AlumniAssociationRequest request) {
        AlumniAssociation association = alumniAssociationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("校友会不存在"));
        
        copyRequestToEntity(request, association);
        return alumniAssociationRepository.save(association);
    }
    
    public void deleteAssociation(Long id) {
        if (!alumniAssociationRepository.existsById(id)) {
            throw new RuntimeException("校友会不存在");
        }
        alumniAssociationRepository.deleteById(id);
    }
    
    @Transactional
    public void deactivateAssociation(Long id) {
        AlumniAssociation association = alumniAssociationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("校友会不存在"));
        association.setActive(false);
        alumniAssociationRepository.save(association);
    }
    
    @Transactional
    public void activateAssociation(Long id) {
        AlumniAssociation association = alumniAssociationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("校友会不存在"));
        association.setActive(true);
        alumniAssociationRepository.save(association);
    }
    
    public Page<AlumniAssociation> getAllAssociations(Pageable pageable) {
        return alumniAssociationRepository.findAllByOrderByCreateTimeDesc(pageable);
    }
    
    public long getActiveAssociationCount() {
        return alumniAssociationRepository.countByActiveTrue();
    }
    
    public Long getTotalMemberCount() {
        Long count = alumniAssociationRepository.getTotalMemberCount();
        return count != null ? count : 0L;
    }
    
    public List<AlumniAssociation> getTopAssociationsByMemberCount(int limit) {
        return alumniAssociationRepository.findTopByMemberCount(
            org.springframework.data.domain.PageRequest.of(0, limit)
        );
    }
    
    private void copyRequestToEntity(AlumniAssociationRequest request, AlumniAssociation association) {
        association.setName(request.getName());
        association.setDescription(request.getDescription());
        association.setPresident(request.getPresident());
        association.setPhone(request.getPhone());
        association.setEmail(request.getEmail());
        association.setAddress(request.getAddress());
        association.setLogoUrl(request.getLogoUrl());
        association.setEstablishmentDate(request.getEstablishmentDate());
        association.setMemberCount(request.getMemberCount());
        association.setActivities(request.getActivities());
        association.setAchievements(request.getAchievements());
        association.setActive(request.getActive());
    }
}