package com.xiaoyou.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "activities")
public class Activity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 200)
    private String title;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(length = 500)
    private String summary;
    
    @Column(length = 100)
    private String organizer;
    
    @Column(length = 255)
    private String imageUrl;
    
    @Column(length = 255)
    private String location;
    
    private LocalDateTime startTime;
    
    private LocalDateTime endTime;
    
    @Column(columnDefinition = "INT DEFAULT 0")
    private Integer maxParticipants;
    
    @Column(columnDefinition = "INT DEFAULT 0")
    private Integer currentParticipants;
    
    @Column(columnDefinition = "DECIMAL(10,2) DEFAULT 0.00")
    private Double fee;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private ActivityStatus status;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private ActivityType type;
    
    @Column(columnDefinition = "INT DEFAULT 0")
    private Integer viewCount;
    
    private Boolean published;
    
    @Column(updatable = false)
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
    
    private LocalDateTime publishTime;
    
    public enum ActivityStatus {
        UPCOMING,    // 即将开始
        ONGOING,     // 进行中
        COMPLETED,   // 已结束
        CANCELLED    // 已取消
    }
    
    public enum ActivityType {
        CONFERENCE,  // 会议
        SEMINAR,     // 研讨会
        NETWORKING,  // 联谊活动
        CHARITY,     // 公益活动
        SPORTS,      // 体育活动
        CULTURAL,    // 文化活动
        OTHER        // 其他
    }
    
    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
        updateTime = LocalDateTime.now();
        if (published == null) {
            published = false;
        }
        if (viewCount == null) {
            viewCount = 0;
        }
        if (currentParticipants == null) {
            currentParticipants = 0;
        }
        if (fee == null) {
            fee = 0.0;
        }
        if (status == null) {
            status = ActivityStatus.UPCOMING;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
        if (published != null && published && publishTime == null) {
            publishTime = LocalDateTime.now();
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
    
    public String getOrganizer() {
        return organizer;
    }
    
    public void setOrganizer(String organizer) {
        this.organizer = organizer;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public Integer getMaxParticipants() {
        return maxParticipants;
    }
    
    public void setMaxParticipants(Integer maxParticipants) {
        this.maxParticipants = maxParticipants;
    }
    
    public Integer getCurrentParticipants() {
        return currentParticipants;
    }
    
    public void setCurrentParticipants(Integer currentParticipants) {
        this.currentParticipants = currentParticipants;
    }
    
    public Double getFee() {
        return fee;
    }
    
    public void setFee(Double fee) {
        this.fee = fee;
    }
    
    public ActivityStatus getStatus() {
        return status;
    }
    
    public void setStatus(ActivityStatus status) {
        this.status = status;
    }
    
    public ActivityType getType() {
        return type;
    }
    
    public void setType(ActivityType type) {
        this.type = type;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public Boolean getPublished() {
        return published;
    }
    
    public void setPublished(Boolean published) {
        this.published = published;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public LocalDateTime getPublishTime() {
        return publishTime;
    }
    
    public void setPublishTime(LocalDateTime publishTime) {
        this.publishTime = publishTime;
    }
}