package com.xiaoyou.repository;

import com.xiaoyou.entity.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommentRepository extends JpaRepository<Comment, Long> {
    
    // 获取帖子的评论（只获取顶级评论，不包括回复）
    @Query("SELECT c FROM Comment c WHERE c.post.id = :postId AND c.parent IS NULL AND c.status = 'PUBLISHED' ORDER BY c.createTime ASC")
    List<Comment> findTopLevelCommentsByPostId(@Param("postId") Long postId);
    
    // 获取帖子的所有评论（包括回复）
    @Query("SELECT c FROM Comment c WHERE c.post.id = :postId AND c.status = 'PUBLISHED' ORDER BY c.createTime ASC")
    List<Comment> findAllCommentsByPostId(@Param("postId") Long postId);
    
    // 获取评论的回复
    @Query("SELECT c FROM Comment c WHERE c.parent.id = :parentId AND c.status = 'PUBLISHED' ORDER BY c.createTime ASC")
    List<Comment> findRepliesByParentId(@Param("parentId") Long parentId);
    
    // 获取用户的评论
    @Query("SELECT c FROM Comment c WHERE c.author.id = :authorId ORDER BY c.createTime DESC")
    Page<Comment> findCommentsByAuthor(@Param("authorId") Long authorId, Pageable pageable);
    
    // 统计帖子的评论数量
    @Query("SELECT COUNT(c) FROM Comment c WHERE c.post.id = :postId AND c.status = 'PUBLISHED'")
    Long countCommentsByPostId(@Param("postId") Long postId);
    
    // 统计用户的评论数量
    @Query("SELECT COUNT(c) FROM Comment c WHERE c.author.id = :authorId AND c.status = 'PUBLISHED'")
    Long countCommentsByAuthor(@Param("authorId") Long authorId);
    
    // 增加点赞数
    @Modifying
    @Query("UPDATE Comment c SET c.likeCount = c.likeCount + 1 WHERE c.id = :id")
    void incrementLikeCount(@Param("id") Long id);
    
    // 减少点赞数
    @Modifying
    @Query("UPDATE Comment c SET c.likeCount = c.likeCount - 1 WHERE c.id = :id AND c.likeCount > 0")
    void decrementLikeCount(@Param("id") Long id);
    
    // 管理员获取所有评论
    @Query("SELECT c FROM Comment c ORDER BY c.createTime DESC")
    Page<Comment> findAllCommentsForAdmin(Pageable pageable);
    
    // 根据状态获取评论
    @Query("SELECT c FROM Comment c WHERE c.status = :status ORDER BY c.createTime DESC")
    Page<Comment> findCommentsByStatus(@Param("status") String status, Pageable pageable);
    
    // 统计各种状态的评论数量
    @Query("SELECT COUNT(c) FROM Comment c WHERE c.status = :status")
    Long countCommentsByStatus(@Param("status") String status);
    
    // 删除帖子的所有评论
    @Modifying
    @Query("DELETE FROM Comment c WHERE c.post.id = :postId")
    void deleteCommentsByPostId(@Param("postId") Long postId);
}