import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

const user = JSON.parse(localStorage.getItem('user')) || null
const admin = JSON.parse(localStorage.getItem('admin')) || null

export default new Vuex.Store({
  state: {
    user: user,
    isLoggedIn: !!user,
    admin: admin,
    isAdminLoggedIn: !!admin
  },
  mutations: {
    SET_USER(state, user) {
      state.user = user
      state.isLoggedIn = !!user
      if (user) {
        localStorage.setItem('user', JSON.stringify(user))
      } else {
        localStorage.removeItem('user')
      }
    },
    LOGOUT(state) {
      state.user = null
      state.isLoggedIn = false
      localStorage.removeItem('user')
    },
    SET_ADMIN(state, admin) {
      state.admin = admin
      state.isAdminLoggedIn = !!admin
      if (admin) {
        localStorage.setItem('admin', JSON.stringify(admin))
      } else {
        localStorage.removeItem('admin')
      }
    },
    CLEAR_ADMIN(state) {
      state.admin = null
      state.isAdminLoggedIn = false
      localStorage.removeItem('admin')
    }
  },
  actions: {
    setUser({ commit }, user) {
      commit('SET_USER', user)
    },
    logout({ commit }) {
      commit('LOGOUT')
    },
    setAdmin({ commit }, admin) {
      commit('SET_ADMIN', admin)
    },
    clearAdmin({ commit }) {
      commit('CLEAR_ADMIN')
    }
  }
})
