package com.xiaoyou.controller;

import com.xiaoyou.dto.ApiResponse;
import com.xiaoyou.dto.JobRequest;
import com.xiaoyou.entity.Job;
import com.xiaoyou.service.JobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/jobs")
@Validated
public class JobController {
    
    @Autowired
    private JobService jobService;
    
    @GetMapping
    public ApiResponse<Page<Job>> getPublishedJobs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) String company) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Job> jobPage;
            
            if (search != null && !search.trim().isEmpty()) {
                jobPage = jobService.searchJobs(search, pageable);
            } else if (type != null && !type.trim().isEmpty()) {
                Job.JobType jobType = Job.JobType.valueOf(type.toUpperCase());
                jobPage = jobService.getJobsByType(jobType, pageable);
            } else if (status != null && !status.trim().isEmpty()) {
                Job.JobStatus jobStatus = Job.JobStatus.valueOf(status.toUpperCase());
                jobPage = jobService.getJobsByStatus(jobStatus, pageable);
            } else if (location != null && !location.trim().isEmpty()) {
                jobPage = jobService.getJobsByLocation(location, pageable);
            } else if (company != null && !company.trim().isEmpty()) {
                jobPage = jobService.getJobsByCompany(company, pageable);
            } else {
                jobPage = jobService.getAllPublishedJobs(pageable);
            }
            
            return ApiResponse.success(jobPage);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @GetMapping("/{id}")
    public ApiResponse<Job> getJobById(@PathVariable Long id) {
        try {
            Job job = jobService.getJobById(id);
            return ApiResponse.success(job);
        } catch (Exception e) {
            return ApiResponse.error(404, e.getMessage());
        }
    }
    
    @PostMapping
    public ApiResponse<Job> createJob(@Valid @RequestBody JobRequest jobRequest) {
        try {
            Job job = jobService.createJob(jobRequest);
            return ApiResponse.success("招聘信息创建成功", job);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @PutMapping("/{id}")
    public ApiResponse<Job> updateJob(@PathVariable Long id, @Valid @RequestBody JobRequest jobRequest) {
        try {
            Job job = jobService.updateJob(id, jobRequest);
            return ApiResponse.success("招聘信息更新成功", job);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteJob(@PathVariable Long id) {
        try {
            jobService.deleteJob(id);
            return ApiResponse.success("招聘信息删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @GetMapping("/admin/all")
    public ApiResponse<Page<Job>> getAllJobs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) Boolean published) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Job> jobPage = jobService.getAllJobsForAdmin(pageable, search, published);
            return ApiResponse.success(jobPage);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @GetMapping("/types")
    public ApiResponse<Job.JobType[]> getJobTypes() {
        return ApiResponse.success(Job.JobType.values());
    }
    
    @GetMapping("/statuses")
    public ApiResponse<Job.JobStatus[]> getJobStatuses() {
        return ApiResponse.success(Job.JobStatus.values());
    }
    
    @PutMapping("/{id}/publish")
    public ApiResponse<Job> togglePublishStatus(@PathVariable Long id) {
        try {
            Job job = jobService.togglePublishStatus(id);
            return ApiResponse.success("发布状态更新成功", job);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
}
