package com.xiaoyou.service;

import com.xiaoyou.dto.ActivityRequest;
import com.xiaoyou.entity.Activity;
import com.xiaoyou.repository.ActivityRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class ActivityService {
    
    @Autowired
    private ActivityRepository activityRepository;
    
    public Page<Activity> getAllPublishedActivities(Pageable pageable) {
        return activityRepository.findByPublishedTrueOrderByStartTimeDesc(pageable);
    }
    
    public Page<Activity> searchActivities(String title, Pageable pageable) {
        if (title == null || title.trim().isEmpty()) {
            return getAllPublishedActivities(pageable);
        }
        return activityRepository.findByTitleContainingIgnoreCaseAndPublishedTrueOrderByStartTimeDesc(title, pageable);
    }
    
    public Page<Activity> getActivitiesByType(Activity.ActivityType type, Pageable pageable) {
        return activityRepository.findByTypeAndPublishedTrueOrderByStartTimeDesc(type, pageable);
    }
    
    public Page<Activity> getActivitiesByStatus(Activity.ActivityStatus status, Pageable pageable) {
        return activityRepository.findByStatusAndPublishedTrueOrderByStartTimeDesc(status, pageable);
    }
    
    public List<Activity> getUpcomingActivities() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime future = now.plusDays(7);
        return activityRepository.findUpcomingActivities(now, future);
    }
    
    public List<Activity> getPopularActivities(int limit) {
        return activityRepository.findPopularActivities(
            org.springframework.data.domain.PageRequest.of(0, limit)
        );
    }
    
    @Transactional
    public Activity getActivityById(Long id) {
        Activity activity = activityRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("活动不存在"));
        activityRepository.incrementViewCount(id);
        return activity;
    }
    
    public Activity createActivity(ActivityRequest activityRequest) {
        validateActivityRequest(activityRequest);
        
        Activity activity = new Activity();
        mapRequestToActivity(activityRequest, activity);
        
        return activityRepository.save(activity);
    }
    
    public Activity updateActivity(Long id, ActivityRequest activityRequest) {
        validateActivityRequest(activityRequest);
        
        Activity activity = activityRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("活动不存在"));
        
        mapRequestToActivity(activityRequest, activity);
        
        return activityRepository.save(activity);
    }
    
    public void deleteActivity(Long id) {
        if (!activityRepository.existsById(id)) {
            throw new RuntimeException("活动不存在");
        }
        activityRepository.deleteById(id);
    }
    
    public Page<Activity> getAllActivities(Pageable pageable) {
        return activityRepository.findAll(pageable);
    }
    
    @Transactional
    public boolean joinActivity(Long activityId) {
        Activity activity = activityRepository.findById(activityId)
                .orElseThrow(() -> new RuntimeException("活动不存在"));
        
        if (activity.getCurrentParticipants() >= activity.getMaxParticipants()) {
            throw new RuntimeException("活动人数已满");
        }
        
        if (activity.getStatus() != Activity.ActivityStatus.UPCOMING) {
            throw new RuntimeException("活动状态不允许报名");
        }
        
        activityRepository.incrementParticipants(activityId);
        return true;
    }
    
    @Transactional
    public boolean leaveActivity(Long activityId) {
        Activity activity = activityRepository.findById(activityId)
                .orElseThrow(() -> new RuntimeException("活动不存在"));
        
        if (activity.getCurrentParticipants() <= 0) {
            throw new RuntimeException("当前无参与者");
        }
        
        activityRepository.decrementParticipants(activityId);
        return true;
    }
    
    @Transactional
    public void updateActivityStatus() {
        LocalDateTime now = LocalDateTime.now();
        
        // 更新活动状态的逻辑
        List<Activity> allActivities = activityRepository.findAll();
        
        for (Activity activity : allActivities) {
            if (activity.getStartTime().isAfter(now)) {
                activity.setStatus(Activity.ActivityStatus.UPCOMING);
            } else if (activity.getEndTime().isAfter(now)) {
                activity.setStatus(Activity.ActivityStatus.ONGOING);
            } else {
                activity.setStatus(Activity.ActivityStatus.COMPLETED);
            }
            activityRepository.save(activity);
        }
    }
    
    private void validateActivityRequest(ActivityRequest request) {
        if (request.getStartTime().isAfter(request.getEndTime())) {
            throw new RuntimeException("开始时间不能晚于结束时间");
        }
        
        if (request.getStartTime().isBefore(LocalDateTime.now())) {
            throw new RuntimeException("开始时间不能早于当前时间");
        }
        
        if (request.getMaxParticipants() != null && request.getMaxParticipants() <= 0) {
            throw new RuntimeException("最大参与人数必须大于0");
        }
        
        if (request.getFee() != null && request.getFee() < 0) {
            throw new RuntimeException("费用不能为负数");
        }
    }
    
    private void mapRequestToActivity(ActivityRequest request, Activity activity) {
        activity.setTitle(request.getTitle());
        activity.setDescription(request.getDescription());
        activity.setSummary(request.getSummary());
        activity.setOrganizer(request.getOrganizer());
        activity.setImageUrl(request.getImageUrl());
        activity.setLocation(request.getLocation());
        activity.setStartTime(request.getStartTime());
        activity.setEndTime(request.getEndTime());
        activity.setMaxParticipants(request.getMaxParticipants());
        activity.setFee(request.getFee());
        activity.setStatus(request.getStatus());
        activity.setType(request.getType());
        activity.setPublished(request.getPublished());
    }
}