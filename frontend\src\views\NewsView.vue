<template>
  <Layout>
    <div class="news-container">
      <div class="news-header">
        <h1>新闻资讯</h1>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索新闻标题"
            prefix-icon="el-icon-search"
            style="width: 300px;"
            @keyup.enter="searchNews"
          >
            <el-button slot="append" icon="el-icon-search" @click="searchNews"></el-button>
          </el-input>
        </div>
      </div>

      <div class="news-content" v-loading="loading">
        <div v-if="newsList.length === 0 && !loading" class="no-data">
          <i class="el-icon-document"></i>
          <p>暂无新闻数据</p>
        </div>
        
        <div class="news-grid" v-else>
          <div 
            class="news-card" 
            v-for="news in newsList" 
            :key="news.id"
            @click="goToDetail(news.id)"
          >
            <div class="news-image">
              <img 
                :src="news.imageUrl || '/images/default-news.jpg'" 
                :alt="news.title"
                @error="handleImageError"
              />
            </div>
            <div class="news-info">
              <h3 class="news-title">{{ news.title }}</h3>
              <p class="news-summary">{{ news.summary || news.content.substring(0, 100) + '...' }}</p>
              <div class="news-meta">
                <span class="author">
                  <i class="el-icon-user"></i>
                  {{ news.author || '管理员' }}
                </span>
                <span class="date">
                  <i class="el-icon-time"></i>
                  {{ formatDate(news.publishTime || news.createTime) }}
                </span>
                <span class="views">
                  <i class="el-icon-view"></i>
                  {{ news.viewCount }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper" v-if="total > 0">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[6, 12, 18, 24]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import api from '@/utils/api'
import Layout from '@/components/Layout.vue'

export default {
  name: 'NewsView',
  components: {
    Layout
  },
  data() {
    return {
      newsList: [],
      loading: false,
      searchKeyword: '',
      currentPage: 1,
      pageSize: 12,
      total: 0
    }
  },
  mounted() {
    this.fetchNews()
  },
  methods: {
    async fetchNews() {
      this.loading = true
      try {
        const response = await api.get('/news', {
          params: {
            page: this.currentPage - 1,
            size: this.pageSize,
            search: this.searchKeyword
          }
        })
        
        if (response.code === 200) {
          this.newsList = response.data.content
          this.total = response.data.totalElements
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('获取新闻失败:', error)
        this.$message.error('获取新闻失败')
      } finally {
        this.loading = false
      }
    },
    searchNews() {
      this.currentPage = 1
      this.fetchNews()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchNews()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchNews()
    },
    goToDetail(newsId) {
      this.$router.push(`/news/${newsId}`)
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    },
    handleImageError(e) {
      e.target.src = '/images/default-news.jpg'
    }
  }
}
</script>

<style scoped>
.news-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #eee;
}

.news-header h1 {
  color: #2c3e50;
  margin: 0;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.news-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.news-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
  transform: scale(1.05);
}

.news-info {
  padding: 20px;
}

.news-title {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-summary {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.news-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.no-data {
  text-align: center;
  padding: 60px 0;
  color: #999;
}

.no-data i {
  font-size: 48px;
  margin-bottom: 10px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}
</style>