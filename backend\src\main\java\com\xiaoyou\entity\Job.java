package com.xiaoyou.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "jobs")
public class Job {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 200)
    private String title;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(length = 500)
    private String summary;
    
    @Column(length = 100)
    private String company;
    
    @Column(length = 100)
    private String location;
    
    @Column(length = 100)
    private String salary;
    
    @Column(length = 100)
    private String experience;
    
    @Column(length = 100)
    private String education;
    
    @Column(length = 255)
    private String imageUrl;
    
    @Column(length = 100)
    private String contactEmail;
    
    @Column(length = 50)
    private String contactPhone;
    
    @Column(length = 100)
    private String contactPerson;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private JobType type;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private JobStatus status;
    
    private LocalDateTime deadline;
    
    @Column(columnDefinition = "INT DEFAULT 0")
    private Integer viewCount;
    
    private Boolean published;
    
    @Column(updatable = false)
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
    
    private LocalDateTime publishTime;
    
    public enum JobType {
        FULL_TIME,    // 全职
        PART_TIME,    // 兼职
        INTERNSHIP,   // 实习
        CONTRACT,     // 合同工
        REMOTE        // 远程工作
    }
    
    public enum JobStatus {
        ACTIVE,       // 招聘中
        PAUSED,       // 暂停招聘
        CLOSED,       // 已关闭
        FILLED        // 已招满
    }
    
    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
        updateTime = LocalDateTime.now();
        if (published == null) {
            published = false;
        }
        if (viewCount == null) {
            viewCount = 0;
        }
        if (status == null) {
            status = JobStatus.ACTIVE;
        }
        if (type == null) {
            type = JobType.FULL_TIME;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
        if (published != null && published && publishTime == null) {
            publishTime = LocalDateTime.now();
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
    
    public String getCompany() {
        return company;
    }
    
    public void setCompany(String company) {
        this.company = company;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public String getSalary() {
        return salary;
    }
    
    public void setSalary(String salary) {
        this.salary = salary;
    }
    
    public String getExperience() {
        return experience;
    }
    
    public void setExperience(String experience) {
        this.experience = experience;
    }
    
    public String getEducation() {
        return education;
    }
    
    public void setEducation(String education) {
        this.education = education;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public String getContactEmail() {
        return contactEmail;
    }
    
    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }
    
    public String getContactPhone() {
        return contactPhone;
    }
    
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }
    
    public String getContactPerson() {
        return contactPerson;
    }
    
    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }
    
    public JobType getType() {
        return type;
    }
    
    public void setType(JobType type) {
        this.type = type;
    }
    
    public JobStatus getStatus() {
        return status;
    }
    
    public void setStatus(JobStatus status) {
        this.status = status;
    }
    
    public LocalDateTime getDeadline() {
        return deadline;
    }
    
    public void setDeadline(LocalDateTime deadline) {
        this.deadline = deadline;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public Boolean getPublished() {
        return published;
    }
    
    public void setPublished(Boolean published) {
        this.published = published;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public LocalDateTime getPublishTime() {
        return publishTime;
    }
    
    public void setPublishTime(LocalDateTime publishTime) {
        this.publishTime = publishTime;
    }
}