package com.xiaoyou.dto;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

public class AlumniAssociationRequest {
    
    @NotBlank(message = "校友会名称不能为空")
    @Size(max = 200, message = "校友会名称长度不能超过200个字符")
    private String name;
    
    @Size(max = 2000, message = "描述长度不能超过2000个字符")
    private String description;
    
    @Size(max = 100, message = "会长姓名长度不能超过100个字符")
    private String president;
    
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String phone;
    
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;
    
    @Size(max = 255, message = "地址长度不能超过255个字符")
    private String address;
    
    @Size(max = 255, message = "Logo URL长度不能超过255个字符")
    private String logoUrl;
    
    private LocalDateTime establishmentDate;
    
    private Integer memberCount;
    
    @Size(max = 2000, message = "活动介绍长度不能超过2000个字符")
    private String activities;
    
    @Size(max = 2000, message = "成就介绍长度不能超过2000个字符")
    private String achievements;
    
    private Boolean active;
    
    // Getters and Setters
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getPresident() {
        return president;
    }
    
    public void setPresident(String president) {
        this.president = president;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getLogoUrl() {
        return logoUrl;
    }
    
    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }
    
    public LocalDateTime getEstablishmentDate() {
        return establishmentDate;
    }
    
    public void setEstablishmentDate(LocalDateTime establishmentDate) {
        this.establishmentDate = establishmentDate;
    }
    
    public Integer getMemberCount() {
        return memberCount;
    }
    
    public void setMemberCount(Integer memberCount) {
        this.memberCount = memberCount;
    }
    
    public String getActivities() {
        return activities;
    }
    
    public void setActivities(String activities) {
        this.activities = activities;
    }
    
    public String getAchievements() {
        return achievements;
    }
    
    public void setAchievements(String achievements) {
        this.achievements = achievements;
    }
    
    public Boolean getActive() {
        return active;
    }
    
    public void setActive(Boolean active) {
        this.active = active;
    }
}