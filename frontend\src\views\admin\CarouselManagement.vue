<template>
  <div class="carousel-management">
    <h2>轮播图管理</h2>
    <p class="description">管理首页轮播图，设置图片、链接和显示顺序</p>
    
    <div class="action-bar">
      <el-button type="primary" icon="el-icon-plus" @click="showAddDialog">添加轮播图</el-button>
      <el-button icon="el-icon-refresh" @click="fetchCarousels">刷新</el-button>
    </div>
    
    <el-table
      :data="carousels"
      style="width: 100%"
      border
      v-loading="loading"
    >
      <el-table-column
        label="预览"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <img 
            :src="scope.row.imageUrl" 
            :alt="scope.row.title" 
            class="carousel-preview"
            @click="previewImage(scope.row.imageUrl)"
          />
        </template>
      </el-table-column>
      
      <el-table-column
        prop="title"
        label="标题"
        width="180"
      >
      </el-table-column>
      
      <el-table-column
        prop="description"
        label="描述"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.description || '无描述' }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="linkUrl"
        label="链接"
        width="180"
      >
        <template slot-scope="scope">
          <el-link 
            :href="scope.row.linkUrl" 
            target="_blank" 
            type="primary" 
            v-if="scope.row.linkUrl"
          >
            {{ scope.row.linkUrl }}
          </el-link>
          <span v-else>无链接</span>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="sortOrder"
        label="排序"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <el-input-number 
            v-model="scope.row.sortOrder" 
            :min="0" 
            :max="999" 
            size="mini"
            @change="(value) => updateCarouselOrder(scope.row.id, value)"
          ></el-input-number>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="isActive"
        label="状态"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isActive"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="(value) => updateCarouselStatus(scope.row.id, value)"
          >
          </el-switch>
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        width="180"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            @click="handleEdit(scope.row)"
            icon="el-icon-edit"
            type="primary"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 添加/编辑轮播图对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="carouselForm" :rules="rules" ref="carouselForm" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="carouselForm.title" placeholder="请输入轮播图标题"></el-input>
        </el-form-item>
        
        <el-form-item label="图片" prop="imageUrl">
          <el-upload
            class="carousel-uploader"
            action="http://localhost:8080/api/upload/image"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="carouselForm.imageUrl" :src="carouselForm.imageUrl" class="carousel-image">
            <i v-else class="el-icon-plus carousel-uploader-icon"></i>
          </el-upload>
          <div class="image-tip">建议尺寸: 1200px × 400px</div>
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            type="textarea" 
            v-model="carouselForm.description" 
            placeholder="请输入轮播图描述（可选）"
            :rows="3"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="链接">
          <el-input v-model="carouselForm.linkUrl" placeholder="请输入点击跳转链接（可选）"></el-input>
        </el-form-item>
        
        <el-form-item label="排序">
          <el-input-number v-model="carouselForm.sortOrder" :min="0" :max="999"></el-input-number>
          <span class="form-tip">数字越小越靠前</span>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch
            v-model="carouselForm.isActive"
            active-text="启用"
            inactive-text="禁用"
          >
          </el-switch>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
      </div>
    </el-dialog>
    
    <!-- 图片预览 -->
    <el-dialog :visible.sync="previewVisible" append-to-body>
      <img width="100%" :src="previewImageUrl" alt="预览">
    </el-dialog>
  </div>
</template>

<script>
import api from '@/utils/api'

export default {
  name: 'CarouselManagement',
  data() {
    return {
      carousels: [],
      loading: false,
      dialogVisible: false,
      dialogTitle: '添加轮播图',
      isEdit: false,
      submitting: false,
      previewVisible: false,
      previewImageUrl: '',
      carouselForm: {
        id: null,
        title: '',
        imageUrl: '',
        linkUrl: '',
        description: '',
        sortOrder: 0,
        isActive: true
      },
      rules: {
        title: [
          { required: true, message: '请输入轮播图标题', trigger: 'blur' },
          { max: 50, message: '标题长度不能超过50个字符', trigger: 'blur' }
        ],
        imageUrl: [
          { required: true, message: '请上传轮播图图片', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.fetchCarousels()
  },
  methods: {
    async fetchCarousels() {
      this.loading = true
      try {
        const response = await api.get('/admin/carousels')
        if (response.code === 200) {
          this.carousels = response.data
        } else {
          this.$message.error(response.message || '获取轮播图失败')
        }
      } catch (error) {
        console.error('获取轮播图失败:', error)
        this.$message.error('获取轮播图失败')
      } finally {
        this.loading = false
      }
    },
    
    showAddDialog() {
      this.isEdit = false
      this.dialogTitle = '添加轮播图'
      this.carouselForm = {
        id: null,
        title: '',
        imageUrl: '',
        linkUrl: '',
        description: '',
        sortOrder: 0,
        isActive: true
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.carouselForm && this.$refs.carouselForm.clearValidate()
      })
    },
    
    handleEdit(row) {
      this.isEdit = true
      this.dialogTitle = '编辑轮播图'
      this.carouselForm = { ...row }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.carouselForm && this.$refs.carouselForm.clearValidate()
      })
    },
    
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除这个轮播图吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await api.delete(`/admin/carousels/${row.id}`)
        if (response.code === 200) {
          this.$message.success('删除成功')
          this.fetchCarousels()
        } else {
          this.$message.error(response.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除轮播图失败:', error)
          this.$message.error('删除失败')
        }
      }
    },
    
    async updateCarouselStatus(id, status) {
      try {
        const response = await api.put(`/admin/carousels/${id}/status`, { isActive: status })
        if (response.code === 200) {
          this.$message.success('状态更新成功')
        } else {
          this.$message.error(response.message || '状态更新失败')
          // 恢复原状态
          const carousel = this.carousels.find(item => item.id === id)
          if (carousel) {
            carousel.isActive = !status
          }
        }
      } catch (error) {
        console.error('更新轮播图状态失败:', error)
        this.$message.error('状态更新失败')
        // 恢复原状态
        const carousel = this.carousels.find(item => item.id === id)
        if (carousel) {
          carousel.isActive = !status
        }
      }
    },
    
    async updateCarouselOrder(id, order) {
      try {
        const response = await api.put(`/admin/carousels/${id}/order`, { sortOrder: order })
        if (response.code === 200) {
          this.$message.success('排序更新成功')
        } else {
          this.$message.error(response.message || '排序更新失败')
          // 恢复原排序
          this.fetchCarousels()
        }
      } catch (error) {
        console.error('更新轮播图排序失败:', error)
        this.$message.error('排序更新失败')
        // 恢复原排序
        this.fetchCarousels()
      }
    },
    
    submitForm() {
      this.$refs.carouselForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true
          try {
            let response
            if (this.isEdit) {
              response = await api.put(`/admin/carousels/${this.carouselForm.id}`, this.carouselForm)
            } else {
              response = await api.post('/admin/carousels', this.carouselForm)
            }
            
            if (response.code === 200) {
              this.$message.success(this.isEdit ? '更新成功' : '添加成功')
              this.dialogVisible = false
              this.fetchCarousels()
            } else {
              this.$message.error(response.message || (this.isEdit ? '更新失败' : '添加失败'))
            }
          } catch (error) {
            console.error(this.isEdit ? '更新轮播图失败:' : '添加轮播图失败:', error)
            this.$message.error(this.isEdit ? '更新失败' : '添加失败')
          } finally {
            this.submitting = false
          }
        }
      })
    },
    
    beforeImageUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        this.$message.error('上传的文件必须是图片!')
        return false
      }
      
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      
      return true
    },
    
    handleImageSuccess(response, file) {
      if (response.code === 200) {
        // 处理后端返回的数据格式
        if (response.data && response.data.url) {
          this.carouselForm.imageUrl = response.data.url
          this.$message.success('图片上传成功')
        } else {
          this.$message.error('图片上传失败：返回数据格式错误')
        }
      } else {
        this.$message.error('图片上传失败：' + (response.message || '未知错误'))
      }
    },
    
    previewImage(url) {
      this.previewImageUrl = url
      this.previewVisible = true
    }
  }
}
</script>

<style scoped>
.carousel-management {
  padding: 20px;
}

.description {
  color: #606266;
  margin-bottom: 20px;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.carousel-preview {
  width: 120px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.3s;
}

.carousel-preview:hover {
  transform: scale(1.05);
}

.carousel-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 300px;
  height: 150px;
}

.carousel-uploader:hover {
  border-color: #409EFF;
}

.carousel-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 150px;
  line-height: 150px;
  text-align: center;
}

.carousel-image {
  width: 300px;
  height: 150px;
  display: block;
  object-fit: cover;
}

.image-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-left: 10px;
}
</style>