package com.xiaoyou.controller;

import com.xiaoyou.dto.ApiResponse;
import com.xiaoyou.dto.NewsRequest;
import com.xiaoyou.entity.News;
import com.xiaoyou.service.NewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/news")
@Validated
public class NewsController {
    
    @Autowired
    private NewsService newsService;
    
    @GetMapping
    public ApiResponse<Page<News>> getPublishedNews(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<News> newsPage;
            
            if (search != null && !search.trim().isEmpty()) {
                newsPage = newsService.searchNews(search, pageable);
            } else {
                newsPage = newsService.getAllPublishedNews(pageable);
            }
            
            return ApiResponse.success(newsPage);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
    
    @GetMapping("/{id}")
    public ApiResponse<News> getNewsById(@PathVariable Long id) {
        try {
            News news = newsService.getNewsById(id);
            return ApiResponse.success(news);
        } catch (Exception e) {
            return ApiResponse.error(404, e.getMessage());
        }
    }
    
    @PostMapping
    public ApiResponse<News> createNews(@Valid @RequestBody NewsRequest newsRequest) {
        try {
            News news = newsService.createNews(newsRequest);
            return ApiResponse.success("新闻创建成功", news);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @PutMapping("/{id}")
    public ApiResponse<News> updateNews(@PathVariable Long id, @Valid @RequestBody NewsRequest newsRequest) {
        try {
            News news = newsService.updateNews(id, newsRequest);
            return ApiResponse.success("新闻更新成功", news);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteNews(@PathVariable Long id) {
        try {
            newsService.deleteNews(id);
            return ApiResponse.success("新闻删除成功", null);
        } catch (Exception e) {
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    @GetMapping("/admin/all")
    public ApiResponse<Page<News>> getAllNews(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<News> newsPage = newsService.getAllNews(pageable);
            return ApiResponse.success(newsPage);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
}