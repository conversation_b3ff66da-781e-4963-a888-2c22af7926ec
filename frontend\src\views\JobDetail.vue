<template>
  <Layout>
    <div class="job-detail" v-loading="loading">
    <div v-if="job" class="job-content">
      <div class="job-header">
        <div class="job-main-info">
          <h1 class="job-title">{{ job.title }}</h1>
          <div class="job-company">
            <i class="el-icon-office-building"></i>
            {{ job.company }}
          </div>
          <div class="job-location" v-if="job.location">
            <i class="el-icon-location"></i>
            {{ job.location }}
          </div>
          <div class="job-salary" v-if="job.salary">
            <i class="el-icon-money"></i>
            {{ job.salary }}
          </div>
        </div>
        
        <div class="job-image" v-if="job.imageUrl">
          <img :src="job.imageUrl" :alt="job.title" />
        </div>
      </div>

      <div class="job-tags">
        <el-tag :type="getJobTypeTagType(job.type)" size="medium">
          {{ getJobTypeText(job.type) }}
        </el-tag>
        <el-tag :type="getJobStatusTagType(job.status)" size="medium">
          {{ getJobStatusText(job.status) }}
        </el-tag>
        <el-tag v-if="job.experience" type="info" size="medium">
          经验要求：{{ job.experience }}
        </el-tag>
        <el-tag v-if="job.education" type="info" size="medium">
          学历要求：{{ job.education }}
        </el-tag>
      </div>

      <div class="job-meta">
        <div class="meta-item">
          <i class="el-icon-view"></i>
          浏览量：{{ job.viewCount }}
        </div>
        <div class="meta-item">
          <i class="el-icon-time"></i>
          发布时间：{{ formatDate(job.publishTime) }}
        </div>
        <div class="meta-item" v-if="job.deadline">
          <i class="el-icon-warning"></i>
          截止时间：{{ formatDate(job.deadline) }}
        </div>
      </div>

      <div class="job-summary" v-if="job.summary">
        <h3>职位概述</h3>
        <p>{{ job.summary }}</p>
      </div>

      <div class="job-description">
        <h3>职位描述</h3>
        <div class="description-content" v-html="formatDescription(job.description)"></div>
      </div>

      <div class="contact-info" v-if="hasContactInfo">
        <h3>联系方式</h3>
        <div class="contact-details">
          <div v-if="job.contactPerson" class="contact-item">
            <i class="el-icon-user"></i>
            <span>联系人：{{ job.contactPerson }}</span>
          </div>
          <div v-if="job.contactEmail" class="contact-item">
            <i class="el-icon-message"></i>
            <span>邮箱：<a :href="'mailto:' + job.contactEmail">{{ job.contactEmail }}</a></span>
          </div>
          <div v-if="job.contactPhone" class="contact-item">
            <i class="el-icon-phone"></i>
            <span>电话：<a :href="'tel:' + job.contactPhone">{{ job.contactPhone }}</a></span>
          </div>
        </div>
      </div>

      <div class="job-actions">
        <el-button type="primary" size="large" @click="applyJob" :disabled="!isJobActive">
          {{ isJobActive ? '立即申请' : '招聘已结束' }}
        </el-button>
        <el-button @click="goBack">返回列表</el-button>
      </div>
    </div>

    <div v-else-if="!loading" class="not-found">
      <el-result
        icon="warning"
        title="招聘信息不存在"
        sub-title="您访问的招聘信息可能已被删除或不存在"
      >
        <template #extra>
          <el-button type="primary" @click="goBack">返回列表</el-button>
        </template>
      </el-result>
    </div>
    </div>
  </Layout>
</template>

<script>
import api from '@/utils/api'
import Layout from '@/components/Layout.vue'

export default {
  name: 'JobDetail',
  components: {
    Layout
  },
  data() {
    return {
      job: null,
      loading: false
    }
  },
  computed: {
    hasContactInfo() {
      return this.job && (this.job.contactPerson || this.job.contactEmail || this.job.contactPhone)
    },
    isJobActive() {
      return this.job && this.job.status === 'ACTIVE' && 
             (!this.job.deadline || new Date(this.job.deadline) > new Date())
    }
  },
  mounted() {
    this.loadJobDetail()
  },
  methods: {
    async loadJobDetail() {
      const jobId = this.$route.params.id
      if (!jobId) {
        this.$message.error('招聘信息ID无效')
        this.goBack()
        return
      }

      this.loading = true
      try {
        const response = await api.get(`/jobs/${jobId}`)
        
        if (response.success) {
          this.job = response.data
        } else {
          this.$message.error(response.message || '加载招聘信息失败')
        }
      } catch (error) {
        console.error('加载招聘信息失败:', error)
        if (error.response && error.response.status === 404) {
          this.$message.error('招聘信息不存在')
        } else {
          this.$message.error('加载招聘信息失败')
        }
      } finally {
        this.loading = false
      }
    },
    
    applyJob() {
      if (!this.isJobActive) {
        this.$message.warning('该职位招聘已结束')
        return
      }
      
      if (this.job.contactEmail) {
        window.open(`mailto:${this.job.contactEmail}?subject=应聘：${this.job.title}`)
      } else {
        this.$message.info('请通过页面显示的联系方式进行申请')
      }
    },
    
    goBack() {
      this.$router.go(-1)
    },
    
    getJobTypeText(type) {
      const typeMap = {
        'FULL_TIME': '全职',
        'PART_TIME': '兼职',
        'INTERNSHIP': '实习',
        'CONTRACT': '合同工',
        'REMOTE': '远程工作'
      }
      return typeMap[type] || type
    },
    
    getJobTypeTagType(type) {
      const typeMap = {
        'FULL_TIME': 'primary',
        'PART_TIME': 'success',
        'INTERNSHIP': 'warning',
        'CONTRACT': 'info',
        'REMOTE': 'danger'
      }
      return typeMap[type] || 'info'
    },
    
    getJobStatusText(status) {
      const statusMap = {
        'ACTIVE': '招聘中',
        'PAUSED': '暂停招聘',
        'CLOSED': '已关闭',
        'FILLED': '已招满'
      }
      return statusMap[status] || status
    },
    
    getJobStatusTagType(status) {
      const statusMap = {
        'ACTIVE': 'success',
        'PAUSED': 'warning',
        'CLOSED': 'info',
        'FILLED': 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    },
    
    formatDescription(description) {
      if (!description) return ''
      return description.replace(/\n/g, '<br>')
    }
  }
}
</script>

<style scoped>
.job-detail {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.job-content {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.job-main-info {
  flex: 1;
}

.job-title {
  font-size: 28px;
  color: #333;
  margin-bottom: 15px;
  font-weight: bold;
}

.job-company,
.job-location,
.job-salary {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.job-company i,
.job-location i,
.job-salary i {
  margin-right: 8px;
  color: #409eff;
}

.job-salary {
  color: #f56c6c;
  font-weight: bold;
  font-size: 18px;
}

.job-image {
  width: 200px;
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  margin-left: 20px;
}

.job-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.job-tags {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.job-meta {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 14px;
}

.meta-item i {
  margin-right: 5px;
  color: #409eff;
}

.job-summary,
.job-description,
.contact-info {
  margin-bottom: 30px;
}

.job-summary h3,
.job-description h3,
.contact-info h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 20px;
  border-left: 4px solid #409eff;
  padding-left: 15px;
}

.job-summary p {
  color: #666;
  line-height: 1.6;
  font-size: 16px;
}

.description-content {
  color: #666;
  line-height: 1.8;
  font-size: 16px;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.contact-item {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 16px;
}

.contact-item i {
  margin-right: 10px;
  color: #409eff;
  width: 16px;
}

.contact-item a {
  color: #409eff;
  text-decoration: none;
}

.contact-item a:hover {
  text-decoration: underline;
}

.job-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.job-actions .el-button {
  margin: 0 10px;
}

.not-found {
  text-align: center;
  padding: 50px 0;
}

@media (max-width: 768px) {
  .job-detail {
    padding: 10px;
  }
  
  .job-content {
    padding: 20px;
  }
  
  .job-header {
    flex-direction: column;
  }
  
  .job-image {
    width: 100%;
    height: 200px;
    margin-left: 0;
    margin-top: 20px;
  }
  
  .job-title {
    font-size: 24px;
  }
  
  .job-meta {
    flex-direction: column;
    gap: 10px;
  }
  
  .job-tags {
    justify-content: center;
  }
  
  .job-actions .el-button {
    display: block;
    width: 100%;
    margin: 10px 0;
  }
}
</style>