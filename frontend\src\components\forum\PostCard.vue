<template>
  <div class="post-card" :class="{ 'pinned': isPinned }" @click="$emit('click')">
    <!-- 置顶标识 -->
    <div class="pinned-badge" v-if="isPinned">
      <i class="el-icon-top"></i>
      置顶
    </div>

    <!-- 帖子头部 -->
    <div class="post-header">
      <div class="author-info">
        <el-avatar :size="40" :src="post.author && post.author.avatar" icon="el-icon-user-solid"></el-avatar>
        <div class="author-details">
          <div class="author-name">{{ (post.author && post.author.name) || '匿名用户' }}</div>
          <div class="post-meta">
            <span class="category-tag" :class="getCategoryClass(post.category)">
              {{ getCategoryName(post.category) }}
            </span>
            <span class="post-time">{{ formatTime(post.createdAt) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 帖子状态 -->
      <div class="post-status">
        <el-tag v-if="post.featured" type="warning" size="mini">精华</el-tag>
        <el-tag v-if="post.status === 'DRAFT'" type="info" size="mini">草稿</el-tag>
        <el-tag v-if="post.status === 'HIDDEN'" type="danger" size="mini">隐藏</el-tag>
      </div>
    </div>

    <!-- 帖子内容 -->
    <div class="post-content">
      <h3 class="post-title">{{ post.title }}</h3>
      <div class="post-summary" v-if="post.summary">
        {{ post.summary }}
      </div>
      <div class="post-excerpt" v-else-if="post.content">
        {{ getExcerpt(post.content) }}
      </div>
      
      <!-- 帖子图片 -->
      <div class="post-images" v-if="post.images && post.images.length > 0">
        <img 
          v-for="(image, index) in post.images.slice(0, 3)" 
          :key="index"
          :src="image" 
          :alt="`图片${index + 1}`"
          class="post-image"
          @click.stop="previewImage(image)"
        />
        <div class="more-images" v-if="post.images.length > 3">
          +{{ post.images.length - 3 }}
        </div>
      </div>
    </div>

    <!-- 帖子统计 -->
    <div class="post-stats">
      <div class="stat-item">
        <i class="el-icon-view"></i>
        <span>{{ formatNumber(post.viewCount) }}</span>
      </div>
      <div class="stat-item like-stat" :class="{ 'liked': post.isLiked }">
        <i class="el-icon-thumb"></i>
        <span>{{ formatNumber(post.likeCount) }}</span>
      </div>
      <div class="stat-item">
        <i class="el-icon-chat-dot-round"></i>
        <span>{{ formatNumber(post.commentCount) }}</span>
      </div>
      <div class="last-reply" v-if="post.lastReplyAt">
        最后回复: {{ formatTime(post.lastReplyAt) }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PostCard',
  props: {
    post: {
      type: Object,
      required: true
    },
    isPinned: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getCategoryName(category) {
      const categoryMap = {
        'GENERAL': '综合讨论',
        'ACADEMIC': '学术交流',
        'CAREER': '职场分享',
        'LIFE': '生活感悟',
        'TECH': '技术讨论',
        'ACTIVITY': '活动通知',
        'HELP': '求助问答',
        'OTHER': '其他'
      }
      return categoryMap[category] || '未分类'
    },

    getCategoryClass(category) {
      const classMap = {
        'GENERAL': 'category-general',
        'ACADEMIC': 'category-academic',
        'CAREER': 'category-career',
        'LIFE': 'category-life',
        'TECH': 'category-tech',
        'ACTIVITY': 'category-activity',
        'HELP': 'category-help',
        'OTHER': 'category-other'
      }
      return classMap[category] || 'category-default'
    },

    formatTime(time) {
      if (!time) return ''
      
      const now = new Date()
      const postTime = new Date(time)
      const diff = now - postTime
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      
      return postTime.toLocaleDateString('zh-CN')
    },

    formatNumber(num) {
      if (!num) return '0'
      if (num < 1000) return num.toString()
      if (num < 10000) return (num / 1000).toFixed(1) + 'k'
      return (num / 10000).toFixed(1) + 'w'
    },

    getExcerpt(content) {
      if (!content) return ''
      // 移除HTML标签
      const text = content.replace(/<[^>]*>/g, '')
      return text.length > 150 ? text.substring(0, 150) + '...' : text
    },

    previewImage(image) {
      // 图片预览功能
      this.$imagePreview([image])
    }
  }
}
</script>

<style scoped>
.post-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e8eaec;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.post-card:hover {
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.post-card.pinned {
  border-left: 4px solid #f56c6c;
  background: linear-gradient(90deg, #fff5f5 0%, #ffffff 20%);
}

.pinned-badge {
  position: absolute;
  top: -1px;
  right: 20px;
  background: #f56c6c;
  color: white;
  padding: 4px 12px;
  border-radius: 0 0 8px 8px;
  font-size: 12px;
  font-weight: 600;
}

.pinned-badge i {
  margin-right: 4px;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.author-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #8c9eff;
}

.category-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  color: white;
}

.category-general { background: #909399; }
.category-academic { background: #409eff; }
.category-career { background: #67c23a; }
.category-life { background: #e6a23c; }
.category-tech { background: #f56c6c; }
.category-activity { background: #909399; }
.category-help { background: #f56c6c; }
.category-other { background: #c0c4cc; }

.post-time {
  color: #8c9eff;
}

.post-status {
  display: flex;
  gap: 8px;
}

.post-content {
  margin-bottom: 15px;
}

.post-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-summary,
.post-excerpt {
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-images {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  position: relative;
}

.post-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.post-image:hover {
  transform: scale(1.05);
}

.more-images {
  width: 80px;
  height: 80px;
  background: rgba(0,0,0,0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
}

.post-stats {
  display: flex;
  align-items: center;
  gap: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f2f5;
  font-size: 13px;
  color: #8c9eff;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  transition: color 0.2s ease;
}

.stat-item:hover {
  color: #409eff;
}

.stat-item i {
  font-size: 14px;
}

.like-stat.liked {
  color: #f56c6c;
}

.like-stat.liked i {
  color: #f56c6c;
}

.last-reply {
  margin-left: auto;
  font-size: 12px;
  color: #c0c4cc;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .post-card {
    padding: 15px;
  }
  
  .post-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .post-stats {
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .last-reply {
    margin-left: 0;
    width: 100%;
    text-align: center;
    margin-top: 8px;
  }
  
  .post-images {
    flex-wrap: wrap;
  }
  
  .post-image,
  .more-images {
    width: 60px;
    height: 60px;
  }
}
</style>