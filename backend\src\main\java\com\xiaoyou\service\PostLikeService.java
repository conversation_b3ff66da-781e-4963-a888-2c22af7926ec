package com.xiaoyou.service;

import com.xiaoyou.entity.Post;
import com.xiaoyou.entity.PostLike;
import com.xiaoyou.entity.User;
import com.xiaoyou.repository.PostLikeRepository;
import com.xiaoyou.repository.PostRepository;
import com.xiaoyou.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class PostLikeService {
    
    @Autowired
    private PostLikeRepository postLikeRepository;
    
    @Autowired
    private PostRepository postRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    // 点赞帖子
    @Transactional
    public boolean likePost(Long postId, Long userId) {
        // 检查是否已经点赞
        Optional<PostLike> existingLike = postLikeRepository.findByPostIdAndUserId(postId, userId);
        if (existingLike.isPresent()) {
            return false; // 已经点赞过了
        }
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        Post post = postRepository.findById(postId)
                .orElseThrow(() -> new RuntimeException("帖子不存在"));
        
        PostLike postLike = new PostLike();
        postLike.setPost(post);
        postLike.setUser(user);
        
        postLikeRepository.save(postLike);
        
        // 增加帖子点赞数
        postRepository.incrementLikeCount(postId);
        
        return true;
    }
    
    // 取消点赞
    @Transactional
    public boolean unlikePost(Long postId, Long userId) {
        Optional<PostLike> existingLike = postLikeRepository.findByPostIdAndUserId(postId, userId);
        if (!existingLike.isPresent()) {
            return false; // 没有点赞过
        }
        
        postLikeRepository.delete(existingLike.get());
        
        // 减少帖子点赞数
        postRepository.decrementLikeCount(postId);
        
        return true;
    }
    
    // 检查用户是否已点赞帖子
    public boolean isPostLikedByUser(Long postId, Long userId) {
        return postLikeRepository.findByPostIdAndUserId(postId, userId).isPresent();
    }
    
    // 获取帖子的点赞列表
    public List<PostLike> getPostLikes(Long postId) {
        return postLikeRepository.findByPostId(postId);
    }
    
    // 获取用户的点赞列表
    public List<PostLike> getUserLikes(Long userId) {
        return postLikeRepository.findByUserId(userId);
    }
    
    // 统计帖子点赞数
    public Long countPostLikes(Long postId) {
        return postLikeRepository.countByPostId(postId);
    }
    
    // 统计用户点赞数
    public Long countUserLikes(Long userId) {
        return postLikeRepository.countByUserId(userId);
    }
}