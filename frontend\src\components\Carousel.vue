<template>
  <div class="carousel-container">
    <el-carousel 
      :interval="4000" 
      type="card" 
      height="400px"
      v-if="carousels.length > 0"
    >
      <el-carousel-item 
        v-for="carousel in carousels" 
        :key="carousel.id"
        @click.native="handleCarouselClick(carousel)"
      >
        <div class="carousel-item">
          <img 
            :src="carousel.imageUrl" 
            :alt="carousel.title"
            class="carousel-image"
          />
          <div class="carousel-content">
            <h3>{{ carousel.title }}</h3>
            <p v-if="carousel.description">{{ carousel.description }}</p>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
    
    <!-- 无轮播图时的占位内容 -->
    <div v-else class="no-carousel">
      <div class="placeholder-content">
        <i class="el-icon-picture" style="font-size: 48px; color: #ddd;"></i>
        <p>暂无轮播图内容</p>
      </div>
    </div>
  </div>
</template>

<script>
import api from '@/utils/api'

export default {
  name: 'Carousel',
  data() {
    return {
      carousels: [],
      loading: false
    }
  },
  created() {
    this.fetchCarousels()
  },
  methods: {
    async fetchCarousels() {
      this.loading = true
      try {
        const response = await api.get('/carousels/active')
        if (response.code === 200) {
          this.carousels = response.data || []
        } else {
          console.error('获取轮播图失败:', response.message)
        }
      } catch (error) {
        console.error('获取轮播图失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    handleCarouselClick(carousel) {
      if (carousel.linkUrl) {
        // 如果是外部链接，在新窗口打开
        if (carousel.linkUrl.startsWith('http')) {
          window.open(carousel.linkUrl, '_blank')
        } else {
          // 内部路由跳转
          this.$router.push(carousel.linkUrl)
        }
      }
    }
  }
}
</script>

<style scoped>
.carousel-container {
  margin-bottom: 40px;
}

.carousel-item {
  position: relative;
  height: 100%;
  cursor: pointer;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  color: white;
  padding: 30px 20px 20px;
}

.carousel-content h3 {
  font-size: 24px;
  margin-bottom: 8px;
  font-weight: bold;
}

.carousel-content p {
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.4;
}

.no-carousel {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border-radius: 10px;
}

.placeholder-content {
  text-align: center;
  color: #999;
}

.placeholder-content p {
  margin-top: 10px;
  font-size: 16px;
}

/* 轮播图指示器样式 */
.carousel-container :deep(.el-carousel__indicator) {
  background-color: rgba(255,255,255,0.5);
}

.carousel-container :deep(.el-carousel__indicator.is-active) {
  background-color: #409eff;
}

/* 轮播图箭头样式 */
.carousel-container :deep(.el-carousel__arrow) {
  background-color: rgba(0,0,0,0.5);
}

.carousel-container :deep(.el-carousel__arrow:hover) {
  background-color: rgba(0,0,0,0.7);
}
</style>