package com.xiaoyou.controller;

import com.xiaoyou.dto.ApiResponse;
import com.xiaoyou.dto.LoginRequest;
import com.xiaoyou.entity.Carousel;
import com.xiaoyou.entity.User;
import com.xiaoyou.service.CarouselService;
import com.xiaoyou.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/admin")
@Validated
@CrossOrigin(origins = "http://localhost:8081")
public class AdminController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private CarouselService carouselService;
    
    @PostMapping("/login")
    public ApiResponse<User> adminLogin(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            User admin = userService.adminLogin(loginRequest);
            admin.setPassword(null); // 不返回密码
            return ApiResponse.success("管理员登录成功", admin);
        } catch (Exception e) {
            return ApiResponse.error(401, e.getMessage());
        }
    }
    
    // 轮播图管理接口
    
    /**
     * 获取所有轮播图
     */
    @GetMapping("/carousels")
    public ApiResponse<List<Carousel>> getAllCarousels() {
        try {
            List<Carousel> carousels = carouselService.getAllCarousels();
            return ApiResponse.success("获取轮播图列表成功", carousels);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取轮播图列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取轮播图
     */
    @GetMapping("/carousels/{id}")
    public ApiResponse<Carousel> getCarouselById(@PathVariable Long id) {
        try {
            Optional<Carousel> carousel = carouselService.getCarouselById(id);
            if (carousel.isPresent()) {
                return ApiResponse.success("获取轮播图成功", carousel.get());
            } else {
                return ApiResponse.error(404, "轮播图不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "获取轮播图失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建轮播图
     */
    @PostMapping("/carousels")
    public ApiResponse<Carousel> createCarousel(@Valid @RequestBody Carousel carousel) {
        try {
            Carousel createdCarousel = carouselService.createCarousel(carousel);
            return ApiResponse.success("创建轮播图成功", createdCarousel);
        } catch (Exception e) {
            return ApiResponse.error(500, "创建轮播图失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新轮播图
     */
    @PutMapping("/carousels/{id}")
    public ApiResponse<Carousel> updateCarousel(@PathVariable Long id, @Valid @RequestBody Carousel carousel) {
        try {
            Carousel updatedCarousel = carouselService.updateCarousel(id, carousel);
            return ApiResponse.success("更新轮播图成功", updatedCarousel);
        } catch (Exception e) {
            return ApiResponse.error(500, "更新轮播图失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除轮播图
     */
    @DeleteMapping("/carousels/{id}")
    public ApiResponse<Void> deleteCarousel(@PathVariable Long id) {
        try {
            carouselService.deleteCarousel(id);
            return ApiResponse.success("删除轮播图成功", null);
        } catch (Exception e) {
            return ApiResponse.error(500, "删除轮播图失败: " + e.getMessage());
        }
    }
    
    /**
     * 切换轮播图状态
     */
    @PutMapping("/carousels/{id}/toggle")
    public ApiResponse<Carousel> toggleCarouselStatus(@PathVariable Long id) {
        try {
            Carousel carousel = carouselService.toggleCarouselStatus(id);
            return ApiResponse.success("切换轮播图状态成功", carousel);
        } catch (Exception e) {
            return ApiResponse.error(500, "切换轮播图状态失败: " + e.getMessage());
        }
    }
}
