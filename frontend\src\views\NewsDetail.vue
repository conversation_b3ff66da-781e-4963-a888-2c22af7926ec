<template>
  <Layout>
    <div class="news-detail-container">
      <div class="news-detail" v-loading="loading">
        <div v-if="news" class="news-content">
          <!-- 返回按钮 -->
          <div class="back-button">
            <el-button 
              type="primary" 
              plain 
              icon="el-icon-arrow-left"
              @click="goBack"
            >
              返回新闻列表
            </el-button>
          </div>

          <!-- 新闻头部 -->
        <div class="news-header">
          <h1 class="news-title">{{ news.title }}</h1>
          <div class="news-meta">
            <span class="author">
              <i class="el-icon-user"></i>
              作者：{{ news.author || '管理员' }}
            </span>
            <span class="date">
              <i class="el-icon-time"></i>
              发布时间：{{ formatDate(news.publishTime || news.createTime) }}
            </span>
            <span class="views">
              <i class="el-icon-view"></i>
              阅读量：{{ news.viewCount }}
            </span>
          </div>
        </div>

        <!-- 新闻图片 -->
        <div class="news-image" v-if="news.imageUrl">
          <img :src="news.imageUrl" :alt="news.title" @error="handleImageError" />
        </div>

        <!-- 新闻摘要 -->
        <div class="news-summary" v-if="news.summary">
          <div class="summary-box">
            <h3>内容摘要</h3>
            <p>{{ news.summary }}</p>
          </div>
        </div>

        <!-- 新闻正文 -->
        <div class="news-body">
          <div class="content" v-html="formatContent(news.content)"></div>
        </div>

        <!-- 分享和操作 -->
        <div class="news-actions">
          <el-button type="primary" icon="el-icon-share" @click="shareNews">
            分享新闻
          </el-button>
          <el-button 
            :type="isFavorited ? 'warning' : 'success'" 
            :icon="isFavorited ? 'el-icon-star-on' : 'el-icon-collection'"
            :loading="favoriteLoading"
            @click="collectNews"
          >
            {{ isFavorited ? '已收藏' : '收藏' }}
          </el-button>
        </div>
        </div>

        <div v-else-if="!loading" class="error-content">
          <el-result
            icon="error"
            title="新闻不存在"
            sub-title="抱歉，您访问的新闻不存在或已被删除"
          >
            <template slot="extra">
              <el-button type="primary" @click="goBack">返回新闻列表</el-button>
            </template>
          </el-result>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import api from '@/utils/api'
import Layout from '@/components/Layout.vue'

export default {
  name: 'NewsDetail',
  components: {
    Layout
  },
  data() {
    return {
      news: null,
      loading: false,
      isFavorited: false,
      favoriteLoading: false
    }
  },
  computed: {
    currentUser() {
      return this.$store.state.user
    },
    isLoggedIn() {
      return this.$store.state.isLoggedIn
    }
  },
  mounted() {
    this.fetchNewsDetail()
  },
  watch: {
    '$route'() {
      this.fetchNewsDetail()
    }
  },
  methods: {
    async fetchNewsDetail() {
      const newsId = this.$route.params.id
      if (!newsId) {
        this.$message.error('新闻ID不存在')
        return
      }

      this.loading = true
      try {
        const response = await api.get(`/news/${newsId}`)
        
        if (response.code === 200) {
          this.news = response.data
          // 获取新闻详情后检查收藏状态
          if (this.isLoggedIn) {
            this.checkFavoriteStatus()
          }
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('获取新闻详情失败:', error)
        this.$message.error('获取新闻详情失败')
      } finally {
        this.loading = false
      }
    },
    goBack() {
      this.$router.push('/news')
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    formatContent(content) {
      if (!content) return ''
      return content.replace(/\n/g, '<br>')
    },
    handleImageError(e) {
      e.target.style.display = 'none'
    },
    shareNews() {
      const url = window.location.href
      if (navigator.share) {
        navigator.share({
          title: this.news.title,
          text: this.news.summary,
          url: url
        })
      } else {
        navigator.clipboard.writeText(url).then(() => {
          this.$message.success('链接已复制到剪贴板')
        }).catch(() => {
          this.$message.error('复制失败，请手动复制链接')
        })
      }
    },
    async checkFavoriteStatus() {
      if (!this.currentUser || !this.news) return
      
      try {
        const response = await api.get('/favorites/check', {
          params: {
            userId: this.currentUser.id,
            itemId: this.news.id,
            itemType: 'news'
          }
        })
        
        if (response.code === 200) {
          this.isFavorited = response.data
        }
      } catch (error) {
        console.error('检查收藏状态失败:', error)
      }
    },
    
    async collectNews() {
      // 检查用户是否登录
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录后再收藏')
        this.$router.push('/login')
        return
      }
      
      if (!this.news) {
        this.$message.error('新闻信息不存在')
        return
      }
      
      this.favoriteLoading = true
      
      try {
        if (this.isFavorited) {
          // 取消收藏
          const response = await api.delete('/favorites/remove', {
            params: {
              userId: this.currentUser.id,
              itemId: this.news.id,
              itemType: 'news'
            }
          })
          
          if (response.code === 200) {
            this.isFavorited = false
            this.$message.success('取消收藏成功')
          } else {
            this.$message.error(response.message || '取消收藏失败')
          }
        } else {
          // 添加收藏
          const response = await api.post('/favorites/add', null, {
            params: {
              userId: this.currentUser.id,
              itemId: this.news.id,
              itemType: 'news'
            }
          })
          
          if (response.code === 200) {
            this.isFavorited = true
            this.$message.success('收藏成功')
          } else {
            this.$message.error(response.message || '收藏失败')
          }
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        this.$message.error('操作失败，请稍后重试')
      } finally {
        this.favoriteLoading = false
      }
    }
  }
}
</script>

<style scoped>
.news-detail-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.back-button {
  margin-bottom: 20px;
}

.news-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.news-title {
  font-size: 28px;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 20px;
}

.news-meta {
  display: flex;
  justify-content: center;
  gap: 30px;
  color: #666;
  font-size: 14px;
}

.news-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.news-image {
  text-align: center;
  margin-bottom: 30px;
}

.news-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.news-summary {
  margin-bottom: 30px;
}

.summary-box {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.summary-box h3 {
  color: #409eff;
  margin: 0 0 10px 0;
  font-size: 16px;
}

.summary-box p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.news-body {
  margin-bottom: 40px;
}

.content {
  font-size: 16px;
  line-height: 1.8;
  color: #333;
}

.content::deep h1,
.content::deep h2,
.content::deep h3 {
  margin: 20px 0 10px 0;
  color: #2c3e50;
}

.content::deep p {
  margin-bottom: 15px;
}

.content::deep img {
  max-width: 100%;
  height: auto;
  margin: 15px 0;
  border-radius: 4px;
}

.news-actions {
  text-align: center;
  padding: 30px 0;
  border-top: 1px solid #eee;
  gap: 20px;
  display: flex;
  justify-content: center;
}

.news-actions .el-button {
  min-width: 120px;
}

.news-actions .el-button--warning {
  background-color: #f39c12;
  border-color: #f39c12;
  color: #fff;
}

.news-actions .el-button--warning:hover {
  background-color: #e67e22;
  border-color: #e67e22;
}

.error-content {
  padding: 40px 0;
}

@media (max-width: 768px) {
  .news-meta {
    flex-direction: column;
    gap: 10px;
  }
  
  .news-title {
    font-size: 24px;
  }
  
  .news-actions {
    flex-direction: column;
    gap: 10px;
  }
}
</style>