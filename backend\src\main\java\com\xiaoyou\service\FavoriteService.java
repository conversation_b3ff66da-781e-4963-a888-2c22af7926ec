package com.xiaoyou.service;

import com.xiaoyou.entity.News;
import com.xiaoyou.entity.Favorite;
import com.xiaoyou.repository.FavoriteRepository;
import com.xiaoyou.repository.NewsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class FavoriteService {

    @Autowired
    private FavoriteRepository favoriteRepository;

    @Autowired
    private NewsRepository newsRepository;

    /**
     * 添加收藏
     */
    public boolean addFavorite(Long userId, Long itemId, String itemType) {
        // 检查是否已经收藏
        if (isFavorited(userId, itemId, itemType)) {
            return false;
        }

        Favorite favorite = new Favorite();
        favorite.setUserId(userId);
        favorite.setItemType(itemType);
        favorite.setItemId(itemId);
        favorite.setCreateTime(LocalDateTime.now());

        favoriteRepository.save(favorite);
        return true;
    }

    /**
     * 取消收藏
     */
    public boolean removeFavorite(Long userId, Long itemId, String itemType) {
        try {
            favoriteRepository.deleteByUserIdAndItemTypeAndItemId(userId, itemType, itemId);
            return true;
        } catch (Exception e) {
            System.err.println("删除收藏失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否已收藏
     */
    public boolean isFavorited(Long userId, Long itemId, String itemType) {
        return favoriteRepository.findByUserIdAndItemTypeAndItemId(userId, itemType, itemId) != null;
    }

    /**
     * 获取用户收藏的新闻列表
     */
    public List<News> getFavoriteNews(Long userId) {
        List<Favorite> favorites = favoriteRepository.findByUserIdAndItemTypeOrderByCreateTimeDesc(userId, "news");
        List<Long> newsIds = favorites.stream()
                .map(Favorite::getItemId)
                .collect(Collectors.toList());
        
        if (newsIds.isEmpty()) {
            return java.util.Collections.emptyList();
        }
        
        return newsRepository.findAllById(newsIds);
    }

    /**
     * 获取用户收藏的新闻数量
     */
    public int getFavoriteNewsCount(Long userId) {
        return favoriteRepository.countByUserIdAndItemType(userId, "news");
    }

    /**
     * 获取用户收藏的活动数量
     */
    public int getFavoriteActivityCount(Long userId) {
        return favoriteRepository.countByUserIdAndItemType(userId, "activity");
    }

    /**
     * 获取用户所有收藏数量
     */
    public int getTotalFavoriteCount(Long userId) {
        return favoriteRepository.countByUserId(userId);
    }
    
    /**
     * 获取用户收藏列表（按类型过滤）
     */
    public List<Favorite> getFavoritesByUser(Long userId, String itemType) {
        if (itemType != null && !itemType.isEmpty()) {
            return favoriteRepository.findByUserIdAndItemTypeOrderByCreateTimeDesc(userId, itemType);
        } else {
            return favoriteRepository.findByUserIdOrderByCreateTimeDesc(userId);
        }
    }
    
    /**
     * 获取用户收藏数量
     */
    public long getFavoriteCount(Long userId) {
        return favoriteRepository.countByUserId(userId);
    }
}
