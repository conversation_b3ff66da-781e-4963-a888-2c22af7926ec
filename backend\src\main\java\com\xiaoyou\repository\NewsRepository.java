package com.xiaoyou.repository;

import com.xiaoyou.entity.News;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface NewsRepository extends JpaRepository<News, Long> {
    
    Page<News> findByPublishedTrueOrderByPublishTimeDesc(Pageable pageable);
    
    Page<News> findByTitleContainingIgnoreCaseAndPublishedTrueOrderByPublishTimeDesc(String title, Pageable pageable);
    
    @Modifying
    @Query("UPDATE News n SET n.viewCount = n.viewCount + 1 WHERE n.id = :id")
    int incrementViewCount(@Param("id") Long id);
}