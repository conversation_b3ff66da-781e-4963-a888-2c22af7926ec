<template>
  <Layout>
    <div class="activity-container">
      <div class="activity-header">
        <h1>校友活动</h1>
        <div class="filter-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索活动标题"
            prefix-icon="el-icon-search"
            style="width: 250px; margin-right: 15px;"
            @keyup.enter="searchActivities"
          >
            <el-button slot="append" icon="el-icon-search" @click="searchActivities"></el-button>
          </el-input>
          
          <el-select v-model="selectedType" placeholder="活动类型" style="width: 150px; margin-right: 15px;" @change="filterActivities">
            <el-option label="全部类型" value=""></el-option>
            <el-option label="会议" value="CONFERENCE"></el-option>
            <el-option label="研讨会" value="SEMINAR"></el-option>
            <el-option label="联谊活动" value="NETWORKING"></el-option>
            <el-option label="公益活动" value="CHARITY"></el-option>
            <el-option label="体育活动" value="SPORTS"></el-option>
            <el-option label="文化活动" value="CULTURAL"></el-option>
            <el-option label="其他" value="OTHER"></el-option>
          </el-select>
          
          <el-select v-model="selectedStatus" placeholder="活动状态" style="width: 150px;" @change="filterActivities">
            <el-option label="全部状态" value=""></el-option>
            <el-option label="即将开始" value="UPCOMING"></el-option>
            <el-option label="进行中" value="ONGOING"></el-option>
            <el-option label="已结束" value="COMPLETED"></el-option>
          </el-select>
        </div>
      </div>

      <div class="activity-content" v-loading="loading">
        <div v-if="activitiesList.length === 0 && !loading" class="no-data">
          <i class="el-icon-calendar"></i>
          <p>暂无活动数据</p>
        </div>
        
        <div class="activity-grid" v-else>
          <div 
            class="activity-card" 
            v-for="activity in activitiesList" 
            :key="activity.id"
            @click="goToDetail(activity.id)"
          >
            <div class="activity-image">
              <img 
                :src="activity.imageUrl || '/images/default-activity.jpg'" 
                :alt="activity.title"
                @error="handleImageError"
              />
              <div class="activity-status" :class="getStatusClass(activity.status)">
                {{ getStatusText(activity.status) }}
              </div>
            </div>
            <div class="activity-info">
              <div class="activity-type">{{ getTypeText(activity.type) }}</div>
              <h3 class="activity-title">{{ activity.title }}</h3>
              <p class="activity-summary">{{ activity.summary || activity.description.substring(0, 100) + '...' }}</p>
              
              <div class="activity-details">
                <div class="detail-item">
                  <i class="el-icon-time"></i>
                  <span>{{ formatDateTime(activity.startTime) }}</span>
                </div>
                <div class="detail-item">
                  <i class="el-icon-location"></i>
                  <span>{{ activity.location }}</span>
                </div>
                <div class="detail-item">
                  <i class="el-icon-user"></i>
                  <span>{{ activity.currentParticipants }}/{{ activity.maxParticipants }}人</span>
                </div>
                <div class="detail-item" v-if="activity.fee > 0">
                  <i class="el-icon-money"></i>
                  <span>¥{{ activity.fee }}</span>
                </div>
                <div class="detail-item" v-else>
                  <i class="el-icon-money"></i>
                  <span>免费</span>
                </div>
              </div>
              
              <div class="activity-meta">
                <span class="organizer">
                  <i class="el-icon-user-solid"></i>
                  {{ activity.organizer || '校友会' }}
                </span>
                <span class="views">
                  <i class="el-icon-view"></i>
                  {{ activity.viewCount }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper" v-if="total > 0">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[6, 12, 18, 24]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          />
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import api from '@/utils/api'
import Layout from '@/components/Layout.vue'

export default {
  name: 'ActivityView',
  components: {
    Layout
  },
  data() {
    return {
      activitiesList: [],
      loading: false,
      searchKeyword: '',
      selectedType: '',
      selectedStatus: '',
      currentPage: 1,
      pageSize: 12,
      total: 0
    }
  },
  mounted() {
    this.fetchActivities()
  },
  methods: {
    async fetchActivities() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage - 1,
          size: this.pageSize
        }
        
        if (this.searchKeyword) {
          params.search = this.searchKeyword
        }
        if (this.selectedType) {
          params.type = this.selectedType
        }
        if (this.selectedStatus) {
          params.status = this.selectedStatus
        }
        
        const response = await api.get('/activities', { params })
        
        if (response.code === 200) {
          this.activitiesList = response.data.content
          this.total = response.data.totalElements
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('获取活动失败:', error)
        this.$message.error('获取活动失败')
      } finally {
        this.loading = false
      }
    },
    searchActivities() {
      this.currentPage = 1
      this.fetchActivities()
    },
    filterActivities() {
      this.currentPage = 1
      this.fetchActivities()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchActivities()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchActivities()
    },
    goToDetail(activityId) {
      this.$router.push(`/activities/${activityId}`)
    },
    formatDateTime(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    getStatusText(status) {
      const statusMap = {
        'UPCOMING': '即将开始',
        'ONGOING': '进行中',
        'COMPLETED': '已结束',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || status
    },
    getStatusClass(status) {
      return `status-${status.toLowerCase()}`
    },
    getTypeText(type) {
      const typeMap = {
        'CONFERENCE': '会议',
        'SEMINAR': '研讨会',
        'NETWORKING': '联谊活动',
        'CHARITY': '公益活动',
        'SPORTS': '体育活动',
        'CULTURAL': '文化活动',
        'OTHER': '其他'
      }
      return typeMap[type] || type
    },
    handleImageError(e) {
      e.target.src = '/images/default-activity.jpg'
    }
  }
}
</script>

<style scoped>
.activity-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #eee;
}

.activity-header h1 {
  color: #2c3e50;
  margin: 0;
}

.filter-box {
  display: flex;
  align-items: center;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.activity-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.activity-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.activity-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.activity-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.activity-card:hover .activity-image img {
  transform: scale(1.05);
}

.activity-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.status-upcoming {
  background-color: #409eff;
}

.status-ongoing {
  background-color: #67c23a;
}

.status-completed {
  background-color: #909399;
}

.status-cancelled {
  background-color: #f56c6c;
}

.activity-info {
  padding: 20px;
}

.activity-type {
  display: inline-block;
  background-color: #f0f9ff;
  color: #409eff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-bottom: 8px;
}

.activity-title {
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.activity-summary {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.activity-details {
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #666;
}

.detail-item i {
  margin-right: 6px;
  width: 14px;
  color: #409eff;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.activity-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.no-data {
  text-align: center;
  padding: 60px 0;
  color: #999;
}

.no-data i {
  font-size: 48px;
  margin-bottom: 10px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

@media (max-width: 768px) {
  .activity-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .filter-box {
    flex-direction: column;
    gap: 10px;
    width: 100%;
  }
  
  .activity-grid {
    grid-template-columns: 1fr;
  }
}
</style>