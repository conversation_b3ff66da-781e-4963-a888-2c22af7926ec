package com.xiaoyou.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "posts")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Post {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    @Column(nullable = false, length = 200)
    private String title;
    
    @NotBlank(message = "内容不能为空")
    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;
    
    @Column(name = "summary", length = 500)
    private String summary;
    
    @Column(name = "image_url")
    private String imageUrl;
    
    // 新增：多图片支持，使用JSON格式存储图片URL数组
    @Column(name = "images_json", columnDefinition = "TEXT")
    private String imagesJson;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "author_id", nullable = false)
    @JsonIgnoreProperties({"password", "email", "phone", "posts", "comments"})
    private User author;
    
    @Column(nullable = false)
    private String category = "GENERAL";
    
    @Column(nullable = false)
    private String status = "PUBLISHED";
    
    @Column(name = "view_count", nullable = false)
    private Integer viewCount = 0;
    
    @Column(name = "like_count", nullable = false)
    private Integer likeCount = 0;
    
    @Column(name = "comment_count", nullable = false)
    private Integer commentCount = 0;
    
    @Column(name = "is_pinned", nullable = false)
    private Boolean isPinned = false;
    
    @Column(name = "is_featured", nullable = false)
    private Boolean isFeatured = false;
    
    @Column(name = "published", nullable = false)
    private Boolean published = true;
    
    @OneToMany(mappedBy = "post", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnoreProperties({"post"})
    private List<Comment> comments;
    
    @OneToMany(mappedBy = "post", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnoreProperties({"post"})
    private List<PostLike> likes;
    
    @Column(name = "create_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    @Column(name = "publish_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;
    
    public enum PostCategory {
        GENERAL("综合讨论"),
        ACADEMIC("学术交流"),
        CAREER("职业发展"),
        LIFE("生活分享"),
        TECHNOLOGY("技术讨论"),
        ALUMNI("校友动态"),
        QUESTION("问答求助"),
        ANNOUNCEMENT("公告通知");
        
        private final String displayName;
        
        PostCategory(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    public enum PostStatus {
        DRAFT("草稿"),
        PUBLISHED("已发布"),
        HIDDEN("已隐藏"),
        DELETED("已删除");
        
        private final String displayName;
        
        PostStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    @PrePersist
    protected void onCreate() {
        if (createTime == null) {
            createTime = LocalDateTime.now();
        }
        if (published != null && published && publishTime == null) {
            publishTime = LocalDateTime.now();
        }
        // 确保默认值不为null
        if (viewCount == null) viewCount = 0;
        if (likeCount == null) likeCount = 0;
        if (commentCount == null) commentCount = 0;
        if (isPinned == null) isPinned = false;
        if (isFeatured == null) isFeatured = false;
        if (published == null) published = true;
        if (category == null) category = "GENERAL";
        if (status == null) status = "PUBLISHED";
    }
    
    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
        if (published != null && published && publishTime == null) {
            publishTime = LocalDateTime.now();
        }
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    // 虚拟字段：为前端提供images数组格式
    @Transient
    public List<String> getImages() {
        List<String> images = new ArrayList<>();
        
        // 优先使用imagesJson中的多图片数据
        if (this.imagesJson != null && !this.imagesJson.trim().isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                List<String> jsonImages = mapper.readValue(this.imagesJson, new TypeReference<List<String>>() {});
                if (jsonImages != null && !jsonImages.isEmpty()) {
                    return jsonImages;
                }
            } catch (Exception e) {
                // JSON解析失败，继续使用单图片逻辑
                System.err.println("解析imagesJson失败: " + e.getMessage());
            }
        }
        
        // Fallback到单图片字段
        if (this.imageUrl != null && !this.imageUrl.trim().isEmpty()) {
            images.add(this.imageUrl);
        }
        
        return images;
    }
    
    // 添加imagesJson的getter和setter
    public String getImagesJson() {
        return imagesJson;
    }
    
    public void setImagesJson(String imagesJson) {
        this.imagesJson = imagesJson;
    }
    
    // 便捷方法：设置images数组
    @Transient
    public void setImages(List<String> images) {
        if (images != null && !images.isEmpty()) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                this.imagesJson = mapper.writeValueAsString(images);
                // 同时设置第一张图片到imageUrl字段（向后兼容）
                this.imageUrl = images.get(0);
            } catch (JsonProcessingException e) {
                System.err.println("序列化images失败: " + e.getMessage());
                // 如果JSON序列化失败，至少保存第一张图片
                this.imageUrl = images.get(0);
            }
        } else {
            this.imagesJson = null;
            this.imageUrl = null;
        }
    }
    
    public User getAuthor() {
        return author;
    }
    
    public void setAuthor(User author) {
        this.author = author;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public Integer getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }
    
    public Integer getCommentCount() {
        return commentCount;
    }
    
    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }
    
    public Boolean getIsPinned() {
        return isPinned;
    }
    
    public void setIsPinned(Boolean isPinned) {
        this.isPinned = isPinned;
    }
    
    public Boolean getIsFeatured() {
        return isFeatured;
    }
    
    public void setIsFeatured(Boolean isFeatured) {
        this.isFeatured = isFeatured;
    }
    
    public Boolean getPublished() {
        return published;
    }
    
    public void setPublished(Boolean published) {
        this.published = published;
    }
    
    public List<Comment> getComments() {
        return comments;
    }
    
    public void setComments(List<Comment> comments) {
        this.comments = comments;
    }
    
    public List<PostLike> getLikes() {
        return likes;
    }
    
    public void setLikes(List<PostLike> likes) {
        this.likes = likes;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public LocalDateTime getPublishTime() {
        return publishTime;
    }
    
    public void setPublishTime(LocalDateTime publishTime) {
        this.publishTime = publishTime;
    }
}