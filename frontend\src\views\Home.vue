<template>
  <Layout>
    <div class="home-container">
      <!-- 轮播图区域 -->
      <div class="carousel-section">
        <Carousel />
      </div>
      
      <!-- 主要内容区域 -->
      <div class="main-content">
        <el-row :gutter="20">
          <!-- 左侧内容区 -->
          <el-col :span="16" :xs="24">
            <!-- 校园新闻 -->
            <div class="news-section">
              <div class="section-header">
                <h3 class="section-title">
                  <i class="el-icon-news"></i>
                  校园新闻
                </h3>
                <a href="#" class="more-link">更多 ></a>
              </div>
              <div class="news-list">
                <div v-if="loading" class="loading-container">
                  <i class="el-icon-loading"></i>
                  <span>加载中...</span>
                </div>
                <div v-else>
                  <div class="news-item" v-for="(news, index) in newsList" :key="news.id || index">
                    <span class="news-date">{{ news.date }}</span>
                    <a href="#" class="news-title">{{ news.title }}</a>
                  </div>
                </div>
              </div>
            </div>

            <!-- 校友动态 -->
            <div class="alumni-section">
              <div class="section-header">
                <h3 class="section-title">
                  <i class="el-icon-user"></i>
                  校友动态
                </h3>
                <a href="#" class="more-link">更多 ></a>
              </div>
              <div class="alumni-list">
                <div class="alumni-item" v-for="(alumni, index) in alumniList" :key="index">
                  <span class="alumni-date">{{ alumni.date }}</span>
                  <a href="#" class="alumni-title">{{ alumni.title }}</a>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 右侧内容区 -->
          <el-col :span="8" :xs="24">
            <!-- 毕业照相馆 -->
            <div class="photo-section">
              <div class="section-header">
                <h3 class="section-title">
                  <i class="el-icon-picture"></i>
                  毕业照相馆
                </h3>
                <a href="#" class="more-link">更多 ></a>
              </div>
              <div class="photo-gallery">
                <div class="photo-item">
                  <div class="photo-placeholder">
                    <i class="el-icon-picture-outline"></i>
                    <span>暂无图片</span>
                  </div>
                  <div class="photo-overlay">
                    <span class="photo-count">2</span>
                  </div>
                  <p class="photo-desc">物理与电子信息学院2016级本科毕业典礼</p>
                </div>
              </div>
            </div>

            <!-- 通知公告 -->
            <div class="notice-section">
              <div class="section-header">
                <h3 class="section-title">
                  <i class="el-icon-bell"></i>
                  通知公告
                </h3>
                <a href="#" class="more-link">更多 ></a>
              </div>
              <div class="notice-banner">
                <div class="notice-content">
                  <h4>洛阳师范学院征集校史资料启事</h4>
                  <button class="close-btn">关闭 X</button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 底部联系我们 -->
        <div class="contact-section">
          <div class="section-header">
            <h3 class="section-title">
              <i class="el-icon-phone"></i>
              联系我们
            </h3>
            <a href="#" class="more-link">更多 ></a>
          </div>
          <div class="contact-info">
            <p>地址：河南省洛阳市伊滨区吉庆路6号</p>
            <p>电话：0379-68618888</p>
            <p>邮箱：<EMAIL></p>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/Layout.vue'
import Carousel from '@/components/Carousel.vue'
import axios from 'axios'

export default {
  name: 'Home',
  components: {
    Layout,
    Carousel
  },
  data() {
    return {
      newsList: [],
      loading: false,
      alumniList: [
        { date: '2025/06/27', title: '校友工作主动融入区域共建共享发展大局' },
        { date: '2025/06/06', title: '校友情深 红色传承——2003级校友返校聚会' },
        { date: '2025/06/03', title: '共筑母校与校友发展共同体——我校举办校友企业专场招聘会' },
        { date: '2025/05/28', title: '2010级校友、河南大张集团董事长张长贵一行回访母校' },
        { date: '2025/05/12', title: '洛阳师范学院教育基金会举行"守护"助学金发放仪式' },
        { date: '2025/05/06', title: '深化校地合作，共谋发展新篇——我校与宜阳县人民政府签署战略合作协议' }
      ]
    }
  },
  mounted() {
    this.fetchNews()
  },
  methods: {
    async fetchNews() {
      try {
        this.loading = true
        console.log('开始获取新闻数据...')
        
        const response = await axios.get('/api/news', {
          params: {
            page: 0,
            size: 6
          }
        })
        
        console.log('API响应:', response.data)
        
        // 后端返回的ApiResponse格式：{ code: 200, message: "成功", data: PageData }
        if (response.data && response.data.code === 200 && response.data.data) {
          const pageData = response.data.data
          const newsData = pageData.content || []
          
          console.log('解析的新闻数据:', newsData)
          
          if (newsData.length > 0) {
            this.newsList = newsData.map(news => ({
              date: this.formatDate(news.publishTime || news.createTime),
              title: news.title,
              id: news.id
            }))
            console.log('处理后的新闻列表:', this.newsList)
          } else {
            console.log('没有已发布的新闻，使用备用数据')
            this.useBackupData()
          }
        } else {
          console.log('API响应格式不正确或无数据，使用备用数据')
          console.log('响应详情:', response.data)
          this.useBackupData()
        }
      } catch (error) {
        console.error('获取新闻数据失败:', error)
        console.log('API调用失败，使用备用数据')
        this.useBackupData()
      } finally {
        this.loading = false
      }
    },
    useBackupData() {
      this.newsList = [
        { date: '2024/06/08', title: '我校隆重举行2024年毕业典礼暨学位授予仪式' },
        { date: '2024/06/07', title: '感恩母校 逐梦未来——学校举办2024届毕业生文艺晚会' },
        { date: '2023/07/20', title: '副省长宋争辉一行莅校检查指导工作' },
        { date: '2023/07/03', title: '我校获文化和旅游部文化和旅游研究基地认定' },
        { date: '2023/04/06', title: '河南省首届高校大学生工训竞赛在我校成功举办' },
        { date: '2023/03/06', title: '学校召开2023年工作会议' }
      ]
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}/${month}/${day}`
    }
  }
}
</script>

<style scoped>
.home-container {
  background: #f5f5f5;
  min-height: 100vh;
}

.carousel-section {
  margin-bottom: 20px;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 40px;
}

/* 通用区块样式 */
.news-section,
.alumni-section,
.photo-section,
.notice-section,
.contact-section {
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: 8px;
  color: #3b82f6;
}

.more-link {
  color: #3b82f6;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.more-link:hover {
  color: #1d4ed8;
}

/* 新闻列表样式 */
.news-list,
.alumni-list {
  padding: 20px;
}

.news-item,
.alumni-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s;
}

.news-item:hover,
.alumni-item:hover {
  background-color: #f8f9fa;
}

.news-item:last-child,
.alumni-item:last-child {
  border-bottom: none;
}

.news-date,
.alumni-date {
  color: #6b7280;
  font-size: 14px;
  min-width: 80px;
  margin-right: 15px;
  flex-shrink: 0;
}

.news-title,
.alumni-title {
  color: #374151;
  text-decoration: none;
  font-size: 14px;
  line-height: 1.5;
  flex: 1;
  transition: color 0.3s;
}

.news-title:hover,
.alumni-title:hover {
  color: #3b82f6;
}

/* 加载状态样式 */
.loading-container {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.loading-container i {
  font-size: 24px;
  margin-right: 8px;
}

/* 照片相馆样式 */
.photo-gallery {
  padding: 20px;
}

.photo-item {
  position: relative;
  text-align: center;
}

.photo-img {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.3s;
}

.photo-img:hover {
  transform: scale(1.02);
}

.photo-placeholder {
  width: 100%;
  height: 180px;
  background: #f5f5f5;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  border: 2px dashed #ddd;
}

.photo-placeholder i {
  font-size: 48px;
  margin-bottom: 8px;
}

.photo-placeholder span {
  font-size: 14px;
}

.photo-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.photo-desc {
  margin-top: 10px;
  font-size: 14px;
  color: #374151;
  line-height: 1.4;
}

/* 通知公告样式 */
.notice-banner {
  padding: 20px;
}

.notice-content {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 20px;
  border-radius: 8px;
  position: relative;
  text-align: center;
}

.notice-content h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 12px;
  opacity: 0.8;
  transition: opacity 0.3s;
}

.close-btn:hover {
  opacity: 1;
}

/* 联系我们样式 */
.contact-info {
  padding: 20px;
}

.contact-info p {
  margin: 8px 0;
  color: #374151;
  font-size: 14px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 0 10px 20px;
  }
  
  .section-header {
    padding: 12px 15px;
  }
  
  .news-list,
  .alumni-list,
  .photo-gallery,
  .notice-banner,
  .contact-info {
    padding: 15px;
  }
  
  .news-date,
  .alumni-date {
    min-width: 70px;
    margin-right: 10px;
    font-size: 12px;
  }
  
  .news-title,
  .alumni-title {
    font-size: 13px;
  }
  
  .photo-img {
    height: 150px;
  }
}
</style>