<template>
  <el-dialog
    title="发布帖子"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    class="create-post-dialog"
  >
    <el-form
      ref="postForm"
      :model="postForm"
      :rules="rules"
      label-width="80px"
      class="post-form"
    >
      <!-- 帖子标题 -->
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="postForm.title"
          placeholder="请输入帖子标题（5-100字符）"
          maxlength="100"
          show-word-limit
          clearable
        />
      </el-form-item>

      <!-- 帖子分类 -->
      <el-form-item label="分类" prop="category">
        <el-select v-model="postForm.category" placeholder="请选择帖子分类" style="width: 200px;">
          <el-option label="综合讨论" value="GENERAL"></el-option>
          <el-option label="学术交流" value="ACADEMIC"></el-option>
          <el-option label="职业发展" value="CAREER"></el-option>
          <el-option label="生活分享" value="LIFE"></el-option>
          <el-option label="技术讨论" value="TECHNOLOGY"></el-option>
          <el-option label="校友动态" value="ALUMNI"></el-option>
          <el-option label="问答求助" value="QUESTION"></el-option>
          <el-option label="公告通知" value="ANNOUNCEMENT"></el-option>
        </el-select>
      </el-form-item>

      <!-- 帖子摘要 -->
      <el-form-item label="摘要" prop="summary">
        <el-input
          v-model="postForm.summary"
          type="textarea"
          :rows="2"
          placeholder="请输入帖子摘要（可选，最多200字符）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 帖子内容 -->
      <el-form-item label="内容" prop="content">
        <div class="editor-container">
          <div class="editor-toolbar">
            <el-button-group>
              <el-button size="mini" @click="insertText('**粗体**')" title="粗体">
                <i class="el-icon-bold"></i>
              </el-button>
              <el-button size="mini" @click="insertText('*斜体*')" title="斜体">
                <i class="el-icon-italic"></i>
              </el-button>
              <el-button size="mini" @click="insertText('~~删除线~~')" title="删除线">
                <i class="el-icon-strikethrough"></i>
              </el-button>
            </el-button-group>
            <el-button-group>
              <el-button size="mini" @click="insertText('# 标题1')" title="标题">H1</el-button>
              <el-button size="mini" @click="insertText('## 标题2')" title="标题">H2</el-button>
              <el-button size="mini" @click="insertText('### 标题3')" title="标题">H3</el-button>
            </el-button-group>
            <el-button-group>
              <el-button size="mini" @click="insertText('- 列表项')" title="无序列表">
                <i class="el-icon-list"></i>
              </el-button>
              <el-button size="mini" @click="insertText('1. 列表项')" title="有序列表">
                <i class="el-icon-numbered-list"></i>
              </el-button>
              <el-button size="mini" @click="insertText('> 引用')" title="引用">
                <i class="el-icon-quote-left"></i>
              </el-button>
            </el-button-group>
            <el-button-group>
              <el-button size="mini" @click="insertText('```\n代码块\n```')" title="代码块">
                <i class="el-icon-code"></i>
              </el-button>
              <el-button size="mini" @click="insertText('[链接文字](链接地址)')" title="链接">
                <i class="el-icon-link"></i>
              </el-button>
            </el-button-group>
          </div>
          <el-input
            ref="contentEditor"
            v-model="postForm.content"
            type="textarea"
            :rows="12"
            placeholder="请输入帖子内容，支持Markdown格式..."
            class="content-editor"
          />
        </div>
      </el-form-item>

      <!-- 图片上传 -->
      <el-form-item label="图片">
        <el-upload
          ref="imageUpload"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :file-list="imageList"
          :on-success="handleImageSuccess"
          :on-remove="handleImageRemove"
          :on-error="handleImageError"
          :before-upload="beforeImageUpload"
          list-type="picture-card"
          accept="image/*"
          :limit="9"
          multiple
        >
          <i class="el-icon-plus"></i>
          <div slot="tip" class="el-upload__tip">
            支持jpg/png/gif格式，单张图片不超过5MB，最多上传9张
          </div>
        </el-upload>
      </el-form-item>

      <!-- 发布选项 -->
      <el-form-item label="发布选项">
        <el-checkbox-group v-model="postOptions">
          <el-checkbox label="anonymous">匿名发布</el-checkbox>
          <el-checkbox label="allowComment">允许评论</el-checkbox>
          <el-checkbox label="draft">保存为草稿</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <!-- 预览区域 -->
    <div class="preview-section" v-if="showPreview">
      <h4>预览效果</h4>
      <div class="preview-content" v-html="renderedContent"></div>
    </div>

    <div slot="footer" class="dialog-footer">
      <div class="footer-left">
        <el-button @click="showPreview = !showPreview" type="text">
          {{ showPreview ? '隐藏预览' : '预览效果' }}
        </el-button>
        <span class="word-count">{{ contentWordCount }}/10000 字</span>
      </div>
      <div class="footer-right">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="saveDraft" :loading="saving">保存草稿</el-button>
        <el-button type="primary" @click="publishPost" :loading="publishing">
          {{ postOptions.includes('draft') ? '保存草稿' : '发布帖子' }}
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import api from '@/utils/api'
import marked from 'marked'

export default {
  name: 'CreatePostDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editPost: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      showPreview: false,
      saving: false,
      publishing: false,
      postForm: {
        title: '',
        category: 'GENERAL',
        summary: '',
        content: '',
        images: [],
        anonymous: false,
        allowComment: true
      },
      postOptions: ['allowComment'],
      imageList: [],
      uploadUrl: 'http://localhost:8080/api/upload/image',
      uploadHeaders: {},
      rules: {
        title: [
          { required: true, message: '请输入帖子标题', trigger: 'blur' },
          { min: 5, max: 100, message: '标题长度在5到100个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择帖子分类', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入帖子内容', trigger: 'blur' },
          { min: 10, message: '内容至少10个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    contentWordCount() {
      return this.postForm.content.length
    },
    renderedContent() {
      if (!this.postForm.content) return ''
      try {
        return marked(this.postForm.content)
      } catch (error) {
        return this.postForm.content
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initForm()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    postOptions: {
      handler(newVal) {
        this.postForm.anonymous = newVal.includes('anonymous')
        this.postForm.allowComment = newVal.includes('allowComment')
      },
      deep: true
    }
  },
  methods: {
    initForm() {
      if (this.editPost) {
        // 编辑模式
        this.postForm = { ...this.editPost }
        this.postOptions = []
        if (this.postForm.anonymous) this.postOptions.push('anonymous')
        if (this.postForm.allowComment) this.postOptions.push('allowComment')
        if (this.postForm.status === 'DRAFT') this.postOptions.push('draft')
        
        // 初始化图片列表
        this.imageList = this.postForm.images && this.postForm.images.length > 0 
          ? this.postForm.images.map((url, index) => ({
              name: `image-${index}`,
              url: url
            }))
          : []
      } else {
        // 新建模式
        this.resetForm()
      }
      
      // 设置上传头部
      const user = this.$store.state.user
      if (user && user.id) {
        this.uploadHeaders['User-Id'] = user.id
      }
    },

    resetForm() {
      this.postForm = {
        title: '',
        category: 'GENERAL',
        summary: '',
        content: '',
        images: [],
        anonymous: false,
        allowComment: true
      }
      this.postOptions = ['allowComment']
      this.imageList = []
      this.showPreview = false
      
      this.$nextTick(() => {
        if (this.$refs.postForm) {
          this.$refs.postForm.clearValidate()
        }
      })
    },

    insertText(text) {
      const textarea = this.$refs.contentEditor.$refs.textarea
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const content = this.postForm.content
      
      this.postForm.content = content.substring(0, start) + text + content.substring(end)
      
      this.$nextTick(() => {
        textarea.focus()
        textarea.setSelectionRange(start + text.length, start + text.length)
      })
    },

    beforeImageUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过5MB!')
        return false
      }
      return true
    },

    handleImageSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.postForm.images.push(response.data.url)
        this.$message.success('图片上传成功')
      } else {
        this.$message.error(response.message || '图片上传失败')
        // 移除失败的文件
        this.imageList = fileList.filter(item => item.uid !== file.uid)
      }
    },

    handleImageRemove(file, fileList) {
      if (file.response && file.response.code === 200) {
        const url = file.response.data.url
        const index = this.postForm.images.indexOf(url)
        if (index > -1) {
          this.postForm.images.splice(index, 1)
        }
      } else if (file.url) {
        const index = this.postForm.images.indexOf(file.url)
        if (index > -1) {
          this.postForm.images.splice(index, 1)
        }
      }
    },

    handleImageError(error, file, fileList) {
      this.$message.error('图片上传失败')
      console.error('Upload error:', error)
    },

    async saveDraft() {
      this.saving = true
      try {
        const postData = {
          ...this.postForm,
          status: 'DRAFT'
        }
        
        let response
        if (this.editPost) {
          response = await api.put(`/posts/${this.editPost.id}`, postData)
        } else {
          response = await api.post('/posts', postData)
        }
        
        if (response.code === 200) {
          this.$message.success('草稿保存成功')
          this.$emit('post-created', response.data)
          this.handleClose()
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('保存草稿失败:', error)
        this.$message.error('保存草稿失败')
      } finally {
        this.saving = false
      }
    },

    async publishPost() {
      this.$refs.postForm.validate(async (valid) => {
        if (!valid) return
        
        if (this.contentWordCount > 10000) {
          this.$message.error('内容不能超过10000字')
          return
        }
        
        this.publishing = true
        try {
          const postData = {
            ...this.postForm,
            status: this.postOptions.includes('draft') ? 'DRAFT' : 'PUBLISHED'
          }
          
          let response
          if (this.editPost) {
            response = await api.put(`/posts/${this.editPost.id}`, postData)
          } else {
            response = await api.post('/posts', postData)
          }
          
          if (response.code === 200) {
            const message = this.postOptions.includes('draft') ? '草稿保存成功' : '帖子发布成功'
            this.$message.success(message)
            this.$emit('post-created', response.data)
            this.handleClose()
          } else {
            this.$message.error(response.message)
          }
        } catch (error) {
          console.error('发布帖子失败:', error)
          this.$message.error('发布帖子失败')
        } finally {
          this.publishing = false
        }
      })
    },

    handleClose() {
      if (this.hasUnsavedChanges()) {
        this.$confirm('有未保存的内容，确定要关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.dialogVisible = false
          this.resetForm()
        }).catch(() => {})
      } else {
        this.dialogVisible = false
        this.resetForm()
      }
    },

    hasUnsavedChanges() {
      return this.postForm.title || this.postForm.content || this.postForm.summary || this.postForm.images.length > 0
    }
  }
}
</script>

<style scoped>
.create-post-dialog {
  max-height: 90vh;
  overflow-y: auto;
}

.post-form {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 10px;
}

.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  background: #f5f7fa;
  padding: 8px 12px;
  border-bottom: 1px solid #dcdfe6;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.content-editor {
  border: none;
}

.content-editor >>> .el-textarea__inner {
  border: none;
  border-radius: 0;
  resize: vertical;
  min-height: 300px;
}

.preview-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e8eaec;
}

.preview-section h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.preview-content {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  line-height: 1.6;
}

.preview-content >>> h1,
.preview-content >>> h2,
.preview-content >>> h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.preview-content >>> p {
  margin-bottom: 10px;
}

.preview-content >>> code {
  background: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.preview-content >>> pre {
  background: #f1f1f1;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
}

.preview-content >>> blockquote {
  border-left: 4px solid #ddd;
  margin: 0;
  padding-left: 15px;
  color: #666;
}

.preview-content >>> ul,
.preview-content >>> ol {
  padding-left: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.word-count {
  font-size: 12px;
  color: #8c9eff;
}

.footer-right {
  display: flex;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .create-post-dialog >>> .el-dialog {
    width: 95% !important;
    margin: 20px auto;
  }
  
  .editor-toolbar {
    flex-direction: column;
    gap: 5px;
  }
  
  .dialog-footer {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .footer-left,
  .footer-right {
    justify-content: center;
  }
}
</style>
