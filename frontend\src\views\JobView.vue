<template>
  <Layout>
    <div class="job-view">
    <div class="header">
      <h1>招聘信息</h1>
      <div class="search-filters">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索职位、公司或地点"
          @keyup.enter="searchJobs"
          class="search-input"
        >
          <template #append>
            <el-button @click="searchJobs" icon="el-icon-search"></el-button>
          </template>
        </el-input>
        
        <el-select v-model="selectedType" placeholder="职位类型" @change="filterJobs" clearable>
          <el-option label="全职" value="FULL_TIME"></el-option>
          <el-option label="兼职" value="PART_TIME"></el-option>
          <el-option label="实习" value="INTERNSHIP"></el-option>
          <el-option label="合同工" value="CONTRACT"></el-option>
          <el-option label="远程工作" value="REMOTE"></el-option>
        </el-select>
        
        <el-select v-model="selectedStatus" placeholder="招聘状态" @change="filterJobs" clearable>
          <el-option label="招聘中" value="ACTIVE"></el-option>
          <el-option label="暂停招聘" value="PAUSED"></el-option>
          <el-option label="已关闭" value="CLOSED"></el-option>
          <el-option label="已招满" value="FILLED"></el-option>
        </el-select>
        
        <el-button @click="resetFilters">重置筛选</el-button>
      </div>
    </div>

    <div class="job-list" v-loading="loading">
      <div v-if="jobs.length === 0 && !loading" class="no-data">
        <el-empty description="暂无招聘信息"></el-empty>
      </div>
      
      <div v-else class="job-cards">
        <div 
          v-for="job in jobs" 
          :key="job.id" 
          class="job-card"
          @click="goToJobDetail(job.id)"
        >
          <div class="job-header">
            <div class="job-title">{{ job.title }}</div>
            <div class="job-salary" v-if="job.salary">{{ job.salary }}</div>
          </div>
          
          <div class="job-company">
            <i class="el-icon-office-building"></i>
            {{ job.company }}
          </div>
          
          <div class="job-location" v-if="job.location">
            <i class="el-icon-location"></i>
            {{ job.location }}
          </div>
          
          <div class="job-summary" v-if="job.summary">
            {{ job.summary }}
          </div>
          
          <div class="job-meta">
            <div class="job-tags">
              <el-tag :type="getJobTypeTagType(job.type)" size="small">
                {{ getJobTypeText(job.type) }}
              </el-tag>
              <el-tag :type="getJobStatusTagType(job.status)" size="small">
                {{ getJobStatusText(job.status) }}
              </el-tag>
            </div>
            
            <div class="job-info">
              <span class="view-count">
                <i class="el-icon-view"></i>
                {{ job.viewCount }}
              </span>
              <span class="publish-time">
                {{ formatDate(job.publishTime) }}
              </span>
            </div>
          </div>
          
          <div class="job-deadline" v-if="job.deadline">
            <i class="el-icon-time"></i>
            截止时间：{{ formatDate(job.deadline) }}
          </div>
        </div>
      </div>
    </div>

    <div class="pagination" v-if="totalPages > 1">
      <el-pagination
        @current-change="handlePageChange"
        :current-page="currentPage + 1"
        :page-size="pageSize"
        :total="totalElements"
        layout="prev, pager, next, total"
      ></el-pagination>
    </div>
    </div>
  </Layout>
</template>

<script>
import api from '@/utils/api'
import Layout from '@/components/Layout.vue'

export default {
  name: 'JobView',
  components: {
    Layout
  },
  data() {
    return {
      jobs: [],
      loading: false,
      searchKeyword: '',
      selectedType: '',
      selectedStatus: '',
      currentPage: 0,
      pageSize: 10,
      totalPages: 0,
      totalElements: 0
    }
  },
  mounted() {
    this.loadJobs()
  },
  methods: {
    async loadJobs() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          size: this.pageSize
        }
        
        if (this.searchKeyword) {
          params.search = this.searchKeyword
        }
        if (this.selectedType) {
          params.type = this.selectedType
        }
        if (this.selectedStatus) {
          params.status = this.selectedStatus
        }
        
        const response = await api.get('/jobs', { params })
        
        if (response.success) {
          this.jobs = response.data.content
          this.totalPages = response.data.totalPages
          this.totalElements = response.data.totalElements
        } else {
          this.$message.error(response.message || '加载招聘信息失败')
        }
      } catch (error) {
        console.error('加载招聘信息失败:', error)
        this.$message.error('加载招聘信息失败')
      } finally {
        this.loading = false
      }
    },
    
    searchJobs() {
      this.currentPage = 0
      this.loadJobs()
    },
    
    filterJobs() {
      this.currentPage = 0
      this.loadJobs()
    },
    
    resetFilters() {
      this.searchKeyword = ''
      this.selectedType = ''
      this.selectedStatus = ''
      this.currentPage = 0
      this.loadJobs()
    },
    
    handlePageChange(page) {
      this.currentPage = page - 1
      this.loadJobs()
    },
    
    goToJobDetail(jobId) {
      this.$router.push(`/jobs/${jobId}`)
    },
    
    getJobTypeText(type) {
      const typeMap = {
        'FULL_TIME': '全职',
        'PART_TIME': '兼职',
        'INTERNSHIP': '实习',
        'CONTRACT': '合同工',
        'REMOTE': '远程工作'
      }
      return typeMap[type] || type
    },
    
    getJobTypeTagType(type) {
      const typeMap = {
        'FULL_TIME': 'primary',
        'PART_TIME': 'success',
        'INTERNSHIP': 'warning',
        'CONTRACT': 'info',
        'REMOTE': 'danger'
      }
      return typeMap[type] || 'info'
    },
    
    getJobStatusText(status) {
      const statusMap = {
        'ACTIVE': '招聘中',
        'PAUSED': '暂停招聘',
        'CLOSED': '已关闭',
        'FILLED': '已招满'
      }
      return statusMap[status] || status
    },
    
    getJobStatusTagType(status) {
      const statusMap = {
        'ACTIVE': 'success',
        'PAUSED': 'warning',
        'CLOSED': 'info',
        'FILLED': 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style scoped>
.job-view {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: 30px;
}

.header h1 {
  color: #333;
  margin-bottom: 20px;
}

.search-filters {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  width: 300px;
}

.job-cards {
  display: grid;
  gap: 20px;
}

.job-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.job-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.job-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.job-salary {
  color: #f56c6c;
  font-weight: bold;
  font-size: 16px;
}

.job-company {
  color: #666;
  margin-bottom: 5px;
}

.job-location {
  color: #666;
  margin-bottom: 10px;
}

.job-company i,
.job-location i,
.job-deadline i {
  margin-right: 5px;
}

.job-summary {
  color: #999;
  margin-bottom: 15px;
  line-height: 1.5;
}

.job-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.job-tags {
  display: flex;
  gap: 8px;
}

.job-info {
  display: flex;
  gap: 15px;
  color: #999;
  font-size: 14px;
}

.view-count i {
  margin-right: 3px;
}

.job-deadline {
  color: #f56c6c;
  font-size: 14px;
  margin-top: 10px;
}

.pagination {
  margin-top: 30px;
  text-align: center;
}

.no-data {
  text-align: center;
  padding: 50px 0;
}

@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    width: 100%;
  }
  
  .job-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .job-meta {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>