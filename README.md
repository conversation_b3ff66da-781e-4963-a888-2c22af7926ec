# 校友信息管理系统

一个基于 Spring Boot + Vue.js 的校友信息管理系统，支持用户注册、登录、新闻资讯等功能，并预留了校友会、校友活动、招聘信息、论坛、校友捐赠等模块。

## 项目结构

```
校友信息管理系统1/
├── backend/                 # Spring Boot 后端
│   ├── src/main/java/com/xiaoyou/
│   │   ├── controller/      # 控制器层
│   │   ├── service/         # 服务层  
│   │   ├── repository/      # 数据访问层
│   │   ├── entity/          # 实体类
│   │   ├── dto/             # 数据传输对象
│   │   └── config/          # 配置类
│   └── src/main/resources/  # 配置文件
├── frontend/                # Vue.js 前端
│   ├── src/
│   │   ├── views/           # 页面组件
│   │   ├── components/      # 公共组件
│   │   ├── router/          # 路由配置
│   │   ├── store/           # Vuex状态管理
│   │   └── utils/           # 工具函数
└── images/                  # 图片资源
```

## 环境要求

### 后端
- Java 8+
- Maven 3.6+
- MySQL 8.0+

### 前端
- Node.js 14+
- npm 6+ 或 yarn

## 数据库配置

1. 创建MySQL数据库：
```sql
CREATE DATABASE xiaoyou CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 初始化测试数据（可选）：
```sql
-- 执行数据库初始化
mysql -u root -p xiaoyou < init_database.sql

-- 插入新闻测试数据
mysql -u root -p xiaoyou < init_news_data.sql
```

3. 数据库连接配置已在 `backend/src/main/resources/application.yml` 中设置：
   - 数据库名：xiaoyou
   - 用户名：root  
   - 密码：123456

## 启动步骤

### 方法一：自动启动（推荐）

```bash
# 确保MySQL服务已启动并创建了xiaoyou数据库
./start.sh
```

### 方法二：手动启动

1. **启动后端**
```bash
cd backend
mvn clean install
mvn spring-boot:run
```
后端将在 http://localhost:8080 启动

2. **启动前端**
```bash
cd frontend
npm install
npm run serve
```
前端将在 http://localhost:3000 启动

## 功能特性

### 已完成功能
- ✅ 用户注册/登录
- ✅ 响应式首页设计
- ✅ 导航菜单系统
- ✅ 新闻资讯模块
  - 新闻列表展示
  - 新闻详情查看
  - 新闻搜索功能
  - 分页功能
  - 阅读量统计
- ✅ 校友信息管理
- ✅ 跨域配置
- ✅ 表单验证

### 待开发功能
- 🚧 校友会管理
- 🚧 校友活动管理
- 🚧 招聘信息发布
- 🚧 校友论坛
- 🚧 校友捐赠系统
- 🚧 管理员后台（增删改查）

## API接口

### 认证相关
- POST `/api/auth/register` - 用户注册
- POST `/api/auth/login` - 用户登录  
- GET `/api/auth/user/{id}` - 获取用户信息

### 新闻相关
- GET `/api/news` - 获取已发布新闻列表（支持搜索和分页）
- GET `/api/news/{id}` - 获取新闻详情
- POST `/api/news` - 创建新闻（管理员功能）
- PUT `/api/news/{id}` - 更新新闻（管理员功能）
- DELETE `/api/news/{id}` - 删除新闻（管理员功能）
- GET `/api/news/admin/all` - 获取所有新闻（包括未发布，管理员功能）

## 技术栈

### 后端
- Spring Boot 2.7.0
- Spring Data JPA
- MySQL 8.0
- Maven

### 前端  
- Vue 2.6
- Element UI 2.15
- Vue Router
- Vuex
- Axios

## 数据库表结构

### 用户表 (users)
- id: 用户ID
- username: 用户名
- password: 密码
- email: 邮箱
- real_name: 真实姓名
- phone: 手机号
- school: 学校
- major: 专业
- graduation_year: 毕业年份
- company: 公司
- position: 职位

### 新闻表 (news)
- id: 新闻ID
- title: 标题
- content: 内容
- summary: 摘要
- author: 作者
- image_url: 图片链接
- view_count: 阅读量
- published: 是否已发布
- create_time: 创建时间
- update_time: 更新时间
- publish_time: 发布时间

## 开发计划

1. **第一阶段**（已完成）
   - 基础框架搭建
   - 用户认证系统
   - 新闻管理系统

2. **第二阶段**（计划中）
   - 管理员后台开发
   - 校友会管理功能
   - 校友活动管理功能

3. **第三阶段**（规划中）
   - 招聘信息系统
   - 校友论坛
   - 校友捐赠系统

## 停止服务

```bash
./stop.sh
```