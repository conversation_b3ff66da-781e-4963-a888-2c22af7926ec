<template>
  <div class="auth-container">
    <div class="auth-card" style="max-width: 500px;">
      <h2 class="auth-title">用户注册</h2>
      <el-form
        ref="registerForm"
        :model="registerForm"
        :rules="registerRules"
        class="auth-form"
        label-width="80px"
        @submit.native.prevent="handleRegister"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名"
          />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="registerForm.email"
            placeholder="请输入邮箱"
          />
        </el-form-item>
        
        <el-form-item label="真实姓名">
          <el-input
            v-model="registerForm.realName"
            placeholder="请输入真实姓名"
          />
        </el-form-item>
        
        <el-form-item label="手机号">
          <el-input
            v-model="registerForm.phone"
            placeholder="请输入手机号"
          />
        </el-form-item>
        
        <el-form-item label="学校">
          <el-input
            v-model="registerForm.school"
            placeholder="请输入学校"
          />
        </el-form-item>
        
        <el-form-item label="专业">
          <el-input
            v-model="registerForm.major"
            placeholder="请输入专业"
          />
        </el-form-item>
        
        <el-form-item label="毕业年份">
          <el-input-number
            v-model="registerForm.graduationYear"
            placeholder="请输入毕业年份"
            :min="1980"
            :max="2030"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="公司">
          <el-input
            v-model="registerForm.company"
            placeholder="请输入公司"
          />
        </el-form-item>
        
        <el-form-item label="职位">
          <el-input
            v-model="registerForm.position"
            placeholder="请输入职位"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            class="auth-button"
            size="large"
            :loading="loading"
            @click="handleRegister"
          >
            注册
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="auth-link">
        <span>已有账号？</span>
        <router-link to="/login">立即登录</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import api from '@/utils/api'

export default {
  name: 'Register',
  data() {
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.registerForm.password) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    
    return {
      loading: false,
      registerForm: {
        username: '',
        password: '',
        confirmPassword: '',
        email: '',
        realName: '',
        phone: '',
        school: '',
        major: '',
        graduationYear: null,
        company: '',
        position: ''
      },
      registerRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 100, message: '密码长度在 6 到 100 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  methods: {
    handleRegister() {
      this.$refs.registerForm.validate(async (valid) => {
        if (!valid) return
        
        this.loading = true
        try {
          const { confirmPassword, ...registerData } = this.registerForm
          const response = await api.post('/auth/register', registerData)
          
          if (response.code === 200) {
            this.$message.success(response.message)
            this.$router.push('/login')
          } else {
            this.$message.error(response.message)
          }
        } catch (error) {
          console.error('Register error:', error)
        } finally {
          this.loading = false
        }
      })
    }
  }
}
</script>