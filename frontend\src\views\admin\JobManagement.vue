<template>
  <div class="job-management">
    <div class="header">
      <h2>招聘信息管理</h2>
      <el-button type="primary" @click="showCreateDialog">
        <i class="el-icon-plus"></i>
        添加招聘信息
      </el-button>
    </div>

    <div class="filters">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索职位标题或公司"
        @keyup.enter="loadJobs"
        style="width: 300px; margin-right: 15px;"
      >
        <el-button slot="append" @click="loadJobs" icon="el-icon-search"></el-button>
      </el-input>
      
      <el-select v-model="filterStatus" placeholder="筛选状态" @change="loadJobs" clearable style="width: 150px;">
        <el-option label="全部" value=""></el-option>
        <el-option label="已发布" value="published"></el-option>
        <el-option label="草稿" value="draft"></el-option>
      </el-select>
    </div>

    <el-table :data="jobs" v-loading="loading" stripe>
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      
      <el-table-column prop="title" label="职位标题" min-width="200">
        <template slot-scope="scope">
          <div class="job-title-cell">
            <div class="title">{{ scope.row.title }}</div>
            <div class="company">{{ scope.row.company }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="location" label="工作地点" width="120"></el-table-column>
      
      <el-table-column prop="salary" label="薪资" width="120"></el-table-column>
      
      <el-table-column prop="type" label="职位类型" width="100">
        <template slot-scope="scope">
          <el-tag :type="getJobTypeTagType(scope.row.type)" size="small">
            {{ getJobTypeText(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="招聘状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getJobStatusTagType(scope.row.status)" size="small">
            {{ getJobStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="published" label="发布状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.published ? 'success' : 'info'" size="small">
            {{ scope.row.published ? '已发布' : '草稿' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="viewCount" label="浏览量" width="80"></el-table-column>
      
      <el-table-column prop="createTime" label="创建时间" width="120">
        <template slot-scope="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" @click="editJob(scope.row)">编辑</el-button>
          <el-button 
            size="mini" 
            :type="scope.row.published ? 'warning' : 'success'"
            @click="togglePublish(scope.row)"
          >
            {{ scope.row.published ? '取消发布' : '发布' }}
          </el-button>
          <el-button size="mini" type="danger" @click="deleteJob(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @current-change="handlePageChange"
        :current-page="currentPage + 1"
        :page-size="pageSize"
        :total="totalElements"
        layout="prev, pager, next, total"
      ></el-pagination>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible" 
      width="800px"
      :before-close="handleDialogClose"
    >
      <el-form :model="jobForm" :rules="jobRules" ref="jobFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="职位标题" prop="title">
              <el-input v-model="jobForm.title" placeholder="请输入职位标题"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公司名称" prop="company">
              <el-input v-model="jobForm.company" placeholder="请输入公司名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工作地点" prop="location">
              <el-input v-model="jobForm.location" placeholder="请输入工作地点"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="薪资范围" prop="salary">
              <el-input v-model="jobForm.salary" placeholder="如：8K-15K"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="职位类型" prop="type">
              <el-select v-model="jobForm.type" placeholder="请选择职位类型" style="width: 100%;">
                <el-option label="全职" value="FULL_TIME"></el-option>
                <el-option label="兼职" value="PART_TIME"></el-option>
                <el-option label="实习" value="INTERNSHIP"></el-option>
                <el-option label="合同工" value="CONTRACT"></el-option>
                <el-option label="远程工作" value="REMOTE"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="招聘状态" prop="status">
              <el-select v-model="jobForm.status" placeholder="请选择招聘状态" style="width: 100%;">
                <el-option label="招聘中" value="ACTIVE"></el-option>
                <el-option label="暂停招聘" value="PAUSED"></el-option>
                <el-option label="已关闭" value="CLOSED"></el-option>
                <el-option label="已招满" value="FILLED"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经验要求" prop="experience">
              <el-input v-model="jobForm.experience" placeholder="如：3-5年"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="学历要求" prop="education">
              <el-input v-model="jobForm.education" placeholder="如：本科及以上"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="截止时间" prop="deadline">
          <el-date-picker
            v-model="jobForm.deadline"
            type="datetime"
            placeholder="选择截止时间"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%;"
          ></el-date-picker>
        </el-form-item>
        
        <el-form-item label="职位摘要" prop="summary">
          <el-input 
            v-model="jobForm.summary" 
            type="textarea" 
            :rows="3"
            placeholder="请输入职位摘要"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="职位描述" prop="description">
          <el-input 
            v-model="jobForm.description" 
            type="textarea" 
            :rows="6"
            placeholder="请输入详细的职位描述"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="职位图片" prop="imageUrl">
          <div class="image-upload-container">
            <el-upload
              class="image-uploader"
              :action="uploadAction"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              :http-request="handleImageUpload"
              :loading="imageUploading"
              accept="image/*"
            >
              <img v-if="jobForm.imageUrl" :src="jobForm.imageUrl" class="uploaded-image">
              <div v-else class="upload-placeholder">
                <i class="el-icon-plus" v-if="!imageUploading"></i>
                <i class="el-icon-loading" v-if="imageUploading"></i>
                <div class="upload-text">{{ imageUploading ? '上传中...' : '点击上传图片' }}</div>
              </div>
            </el-upload>
            <div class="image-actions" v-if="jobForm.imageUrl">
              <el-button size="mini" @click="previewImage">预览</el-button>
              <el-button size="mini" type="danger" @click="removeImage">删除</el-button>
            </div>
          </div>
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="jobForm.contactPerson" placeholder="请输入联系人"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系邮箱" prop="contactEmail">
              <el-input v-model="jobForm.contactEmail" placeholder="请输入联系邮箱"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="jobForm.contactPhone" placeholder="请输入联系电话"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="发布状态">
          <el-switch v-model="jobForm.published" active-text="发布" inactive-text="草稿"></el-switch>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveJob" :loading="saving">保存</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" :visible.sync="imagePreviewVisible" width="600px">
      <div class="image-preview-container">
        <img :src="jobForm.imageUrl" style="width: 100%; height: auto;" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'JobManagement',
  data() {
    return {
      jobs: [],
      loading: false,
      saving: false,
      searchKeyword: '',
      filterStatus: '',
      currentPage: 0,
      pageSize: 10,
      totalElements: 0,
      imageUploading: false,
      imagePreviewVisible: false,
      uploadAction: 'http://localhost:8080/api/upload/image',
      
      dialogVisible: false,
      isEdit: false,
      editingJobId: null,
      
      jobForm: {
        title: '',
        description: '',
        summary: '',
        company: '',
        location: '',
        salary: '',
        experience: '',
        education: '',
        imageUrl: '',
        contactEmail: '',
        contactPhone: '',
        contactPerson: '',
        type: 'FULL_TIME',
        status: 'ACTIVE',
        deadline: '',
        published: false
      },
      
      jobRules: {
        title: [
          { required: true, message: '请输入职位标题', trigger: 'blur' },
          { max: 200, message: '标题长度不能超过200个字符', trigger: 'blur' }
        ],
        company: [
          { required: true, message: '请输入公司名称', trigger: 'blur' },
          { max: 100, message: '公司名称长度不能超过100个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入职位描述', trigger: 'blur' }
        ],
        contactEmail: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑招聘信息' : '添加招聘信息'
    }
  },
  mounted() {
    // 延迟加载，避免重复调用
    this.$nextTick(() => {
      this.loadJobs()
    })
  },
  methods: {
    async loadJobs() {
      // 防止重复调用
      if (this.loading) {
        return
      }
      
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          size: this.pageSize
        }
        
        // 添加搜索和筛选参数
        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.search = this.searchKeyword.trim()
        }
        
        if (this.filterStatus && this.filterStatus !== '') {
          if (this.filterStatus === 'published') {
            params.published = true
          } else if (this.filterStatus === 'draft') {
            params.published = false
          }
        }
        
        console.log('Loading jobs with params:', params)
        const response = await axios.get('/api/jobs/admin/all', { params })
        console.log('Jobs response:', response.data)
        
        // 检查响应是否成功 - 支持success字段或code=200
        if (response.data && (response.data.success === true || response.data.code === 200)) {
          this.jobs = response.data.data.content || []
          this.totalElements = response.data.data.totalElements || 0
          console.log('Loaded jobs:', this.jobs.length, 'Total:', this.totalElements)
        } else {
          // 只有在真正出错时才显示错误消息
          console.error('加载招聘信息失败:', response.data)
          this.jobs = []
          this.totalElements = 0
        }
      } catch (error) {
        console.error('加载招聘信息失败:', error)
        this.$message({
          message: '加载招聘信息失败',
          type: 'error'
        })
        this.jobs = []
        this.totalElements = 0
      } finally {
        this.loading = false
      }
    },
    
    handlePageChange(page) {
      this.currentPage = page - 1
      this.loadJobs()
    },
    
    showCreateDialog() {
      this.isEdit = false
      this.editingJobId = null
      this.resetForm()
      this.dialogVisible = true
    },
    
    editJob(job) {
      this.isEdit = true
      this.editingJobId = job.id
      this.jobForm = {
        title: job.title,
        description: job.description,
        summary: job.summary || '',
        company: job.company,
        location: job.location || '',
        salary: job.salary || '',
        experience: job.experience || '',
        education: job.education || '',
        imageUrl: job.imageUrl || '',
        contactEmail: job.contactEmail || '',
        contactPhone: job.contactPhone || '',
        contactPerson: job.contactPerson || '',
        type: job.type,
        status: job.status,
        deadline: job.deadline ? this.formatDateTimeForEdit(job.deadline) : '',
        published: job.published
      }
      this.dialogVisible = true
    },
    
    async saveJob() {
      try {
        await this.$refs.jobFormRef.validate()
        
        this.saving = true
        
        const url = this.isEdit ? `/api/jobs/${this.editingJobId}` : '/api/jobs'
        const method = this.isEdit ? 'put' : 'post'
        
        const response = await axios[method](url, this.jobForm)
        
        if (response.data.success) {
          this.$message.success(response.data.message || '保存成功')
          this.dialogVisible = false
          this.loadJobs()
        } else {
          this.$message.error(response.data.message || '保存失败')
        }
      } catch (error) {
        console.error('加载招聘信息失败:', error)
        this.jobs = []
        this.totalElements = 0
        
        // 只有在网络错误或服务器错误时才显示消息
        // 避免在正常的空数据情况下显示错误消息
        if (error.response && error.response.status >= 500) {
          this.$message({
            message: '服务器错误，请稍后重试',
            type: 'error',
            showClose: true,
            duration: 3000
          })
        } else if (!error.response) {
          this.$message({
            message: '网络连接失败，请检查网络',
            type: 'error',
            showClose: true,
            duration: 3000
          })
        }
      } finally {
        this.saving = false
      }
    },
    
    async togglePublish(job) {
      try {
        const response = await axios.put(`/api/jobs/${job.id}/publish`)
        
        if (response.data.success) {
          const newStatus = response.data.data.published
          this.$message({
            message: newStatus ? '发布成功' : '取消发布成功',
            type: 'success'
          })
          this.loadJobs()
        } else {
          this.$message({
            message: response.data.message || '操作失败',
            type: 'error'
          })
        }
      } catch (error) {
        console.error('切换发布状态失败:', error)
        this.$message({
          message: '操作失败',
          type: 'error',
          showClose: true,
          duration: 3000
        })
      }
    },
    
    deleteJob(job) {
      this.$confirm(`确定要删除招聘信息"${job.title}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await axios.delete(`/api/jobs/${job.id}`)
          
          if (response.data.success) {
            this.$message.success('删除成功')
            this.loadJobs()
          } else {
            this.$message.error(response.data.message || '删除失败')
          }
        } catch (error) {
          console.error('删除招聘信息失败:', error)
          this.$message({
            message: '删除失败',
            type: 'error',
            showClose: true,
            duration: 3000
          })
        }
      })
    },
    
    resetForm() {
      this.jobForm = {
        title: '',
        description: '',
        summary: '',
        company: '',
        location: '',
        salary: '',
        experience: '',
        education: '',
        imageUrl: '',
        contactEmail: '',
        contactPhone: '',
        contactPerson: '',
        type: 'FULL_TIME',
        status: 'ACTIVE',
        deadline: '',
        published: false
      }
      if (this.$refs.jobFormRef) {
        this.$refs.jobFormRef.resetFields()
      }
    },
    
    handleDialogClose() {
      this.dialogVisible = false
      this.resetForm()
    },
    
    getJobTypeText(type) {
      const typeMap = {
        'FULL_TIME': '全职',
        'PART_TIME': '兼职',
        'INTERNSHIP': '实习',
        'CONTRACT': '合同工',
        'REMOTE': '远程工作'
      }
      return typeMap[type] || type
    },
    
    getJobTypeTagType(type) {
      const typeMap = {
        'FULL_TIME': 'primary',
        'PART_TIME': 'success',
        'INTERNSHIP': 'warning',
        'CONTRACT': 'info',
        'REMOTE': 'danger'
      }
      return typeMap[type] || 'info'
    },
    
    getJobStatusText(status) {
      const statusMap = {
        'ACTIVE': '招聘中',
        'PAUSED': '暂停招聘',
        'CLOSED': '已关闭',
        'FILLED': '已招满'
      }
      return statusMap[status] || status
    },
    
    getJobStatusTagType(status) {
      const statusMap = {
        'ACTIVE': 'success',
        'PAUSED': 'warning',
        'CLOSED': 'info',
        'FILLED': 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    },
    
    // 图片上传相关方法
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG/GIF 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      
      this.imageUploading = true
      return true
    },
    
    async handleImageUpload(options) {
      const { file } = options
      
      try {
        this.imageUploading = true
        
        const formData = new FormData()
        formData.append('file', file)
        
        console.log('开始上传图片:', file.name, file.size, file.type)
        
        // 使用完整的URL路径，确保请求正确发送
        const response = await axios.post('http://localhost:8080/api/upload/image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            console.log('上传进度:', percentCompleted + '%')
          }
        })
        
        console.log('上传响应完整数据:', JSON.stringify(response.data, null, 2))
        
        console.log('检查响应数据结构:', response.data)
        console.log('响应code:', response.data.code)
        console.log('响应message:', response.data.message)
        console.log('响应data:', response.data.data)
        
        // 检查响应是否成功 - 支持success字段或code=200
        if (response.data && (response.data.success === true || response.data.code === 200)) {
          // 修复URL解析逻辑 - response.data.data就是包含url的Map对象
          const imageUrl = response.data.data ? response.data.data.url : null
          
          console.log('解析到的图片URL:', imageUrl)
          console.log('当前jobForm.imageUrl:', this.jobForm.imageUrl)
          
          if (imageUrl) {
            // 使用Vue.set确保响应式更新
            this.$set(this.jobForm, 'imageUrl', imageUrl)
            
            // 强制更新视图
            this.$nextTick(() => {
              console.log('更新后的jobForm.imageUrl:', this.jobForm.imageUrl)
              this.$forceUpdate()
            })
            
            // 使用Element UI的成功消息方法，确保显示绿色
            this.$message.success('图片上传成功')
            
            console.log('图片上传成功，jobForm.imageUrl已设置为:', this.jobForm.imageUrl)
            
          } else {
            console.error('服务器返回的数据结构:', response.data)
            throw new Error('服务器返回的图片URL为空')
          }
        } else {
          console.error('服务器返回失败:', response.data)
          throw new Error(response.data.message || '服务器返回上传失败')
        }
      } catch (error) {
        console.error('图片上传失败详细信息:', error)
        
        // 确保显示错误消息
        let errorMessage = '图片上传失败'
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.message) {
          errorMessage = error.message
        }
        
        // 使用Element UI的错误消息方法
        this.$message({
          message: errorMessage,
          type: 'error',
          showClose: true,
          duration: 5000
        })
      } finally {
        this.imageUploading = false
      }
    },
    
    previewImage() {
      this.imagePreviewVisible = true
    },
    
    removeImage() {
      this.jobForm.imageUrl = ''
      this.$message.success('图片已删除')
    }
  }
}
</script>

<style scoped>
.job-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #333;
}

.filters {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.job-title-cell .title {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.job-title-cell .company {
  color: #666;
  font-size: 12px;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 20px;
}

/* 图片上传样式 */
.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
}

.image-uploader {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  width: 200px;
  height: 200px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409eff;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #8c939d;
  text-align: center;
}

.upload-placeholder i {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
  line-height: 1.4;
}

.image-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

.image-preview-container {
  text-align: center;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .filters {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .filters .el-input,
  .filters .el-select {
    width: 100% !important;
  }
}
</style>