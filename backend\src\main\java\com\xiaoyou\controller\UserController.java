package com.xiaoyou.controller;

import com.xiaoyou.dto.ApiResponse;
import com.xiaoyou.entity.User;
import com.xiaoyou.entity.News;
import com.xiaoyou.entity.Favorite;
import com.xiaoyou.service.UserService;
import com.xiaoyou.service.FavoriteService;
import com.xiaoyou.service.NewsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;
    
    @Autowired
    private FavoriteService favoriteService;
    
    @Autowired
    private NewsService newsService;

    /**
     * 获取用户个人资料
     */
    @GetMapping("/profile")
    public ApiResponse<User> getUserProfile(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            if (userId == null) {
                return ApiResponse.error("用户未登录");
            }
            
            User user = userService.findById(userId);
            if (user != null) {
                // 清除敏感信息
                user.setPassword(null);
                return ApiResponse.success(user);
            } else {
                return ApiResponse.error("用户不存在");
            }
        } catch (Exception e) {
            return ApiResponse.error("获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户个人资料
     */
    @PutMapping("/profile")
    public ApiResponse<String> updateUserProfile(@RequestBody User userInfo, HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            if (userId == null) {
                return ApiResponse.error("用户未登录");
            }
            
            User existingUser = userService.findById(userId);
            if (existingUser == null) {
                return ApiResponse.error("用户不存在");
            }
            
            // 更新允许修改的字段
            existingUser.setRealName(userInfo.getRealName());
            existingUser.setEmail(userInfo.getEmail());
            existingUser.setPhone(userInfo.getPhone());
            existingUser.setGraduationYear(userInfo.getGraduationYear());
            existingUser.setMajor(userInfo.getMajor());
            existingUser.setCompany(userInfo.getCompany());
            existingUser.setPosition(userInfo.getPosition());
            existingUser.setBio(userInfo.getBio());
            
            userService.save(existingUser);
            return ApiResponse.success("个人资料更新成功", "更新成功");
        } catch (Exception e) {
            return ApiResponse.error("更新个人资料失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户头像
     */
    @PutMapping("/avatar")
    public ApiResponse<String> updateAvatar(@RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        try {
            Long userId = getUserIdFromRequest(httpRequest);
            if (userId == null) {
                return ApiResponse.error("用户未登录");
            }
            
            String avatarUrl = request.get("avatar");
            if (avatarUrl == null || avatarUrl.trim().isEmpty()) {
                return ApiResponse.error("头像URL不能为空");
            }
            
            User user = userService.findById(userId);
            if (user == null) {
                return ApiResponse.error("用户不存在");
            }
            
            user.setAvatar(avatarUrl);
            userService.save(user);
            
            return ApiResponse.success("头像更新成功", "更新成功");
        } catch (Exception e) {
            return ApiResponse.error("头像更新失败：" + e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    @PutMapping("/password")
    public ApiResponse<String> changePassword(@RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        try {
            Long userId = getUserIdFromRequest(httpRequest);
            if (userId == null) {
                return ApiResponse.error("用户未登录");
            }
            
            String currentPassword = request.get("currentPassword");
            String newPassword = request.get("newPassword");
            
            if (currentPassword == null || newPassword == null) {
                return ApiResponse.error("当前密码和新密码不能为空");
            }
            
            if (newPassword.length() < 6) {
                return ApiResponse.error("新密码长度不能少于6位");
            }
            
            User user = userService.findById(userId);
            if (user == null) {
                return ApiResponse.error("用户不存在");
            }
            
            // 验证当前密码
            if (!userService.verifyPassword(user, currentPassword)) {
                return ApiResponse.error("当前密码错误");
            }
            
            // 更新密码
            user.setPassword(userService.encodePassword(newPassword));
            userService.save(user);
            
            return ApiResponse.success("密码修改成功", "修改成功");
        } catch (Exception e) {
            return ApiResponse.error("密码修改失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/stats")
    public ApiResponse<Map<String, Object>> getUserStats(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            if (userId == null) {
                return ApiResponse.error("用户未登录");
            }
            
            Map<String, Object> stats = userService.getUserStats(userId);
            return ApiResponse.success(stats);
        } catch (Exception e) {
            return ApiResponse.error("获取用户统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户收藏的新闻列表
     */
    @GetMapping("/favorites/news")
    public ApiResponse<List<News>> getFavoriteNews(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            if (userId == null) {
                return ApiResponse.error("用户未登录");
            }
            
            List<News> favoriteNews = favoriteService.getFavoriteNews(userId);
            return ApiResponse.success(favoriteNews);
        } catch (Exception e) {
            return ApiResponse.error("获取收藏新闻失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户收藏的活动列表
     */
    @GetMapping("/favorites/activities")
    public ApiResponse<List<Object>> getFavoriteActivities(HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            if (userId == null) {
                return ApiResponse.error("用户未登录");
            }
            
            // 暂时返回空列表，因为还没有活动功能
            List<Object> favoriteActivities = new ArrayList<>();
            return ApiResponse.success(favoriteActivities);
        } catch (Exception e) {
            return ApiResponse.error("获取收藏活动失败：" + e.getMessage());
        }
    }
    
    /**
     * 添加收藏
     */
    @PostMapping("/favorites/{type}/{itemId}")
    public ApiResponse<String> addFavorite(@PathVariable String type, 
                                          @PathVariable Long itemId, 
                                          HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            if (userId == null) {
                return ApiResponse.error("用户未登录");
            }
            
            boolean success = favoriteService.addFavorite(userId, itemId, type);
            if (success) {
                // 更新用户收藏数量
                userService.updateFavoriteCount(userId, 1);
                return ApiResponse.success("收藏成功");
            } else {
                return ApiResponse.error("已经收藏过了");
            }
        } catch (Exception e) {
            return ApiResponse.error("收藏失败：" + e.getMessage());
        }
    }
    
    /**
     * 取消收藏
     */
    @DeleteMapping("/favorites/{type}/{itemId}")
    public ApiResponse<String> removeFavorite(@PathVariable String type, 
                                             @PathVariable Long itemId, 
                                             HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            if (userId == null) {
                return ApiResponse.error("用户未登录");
            }
            
            boolean success = favoriteService.removeFavorite(userId, itemId, type);
            if (success) {
                // 更新用户收藏数量
                userService.updateFavoriteCount(userId, -1);
                return ApiResponse.success("取消收藏成功");
            } else {
                return ApiResponse.error("未找到收藏记录");
            }
        } catch (Exception e) {
            return ApiResponse.error("取消收藏失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查是否已收藏
     */
    @GetMapping("/favorites/{type}/{itemId}/check")
    public ApiResponse<Boolean> checkFavorite(@PathVariable String type, 
                                             @PathVariable Long itemId, 
                                             HttpServletRequest request) {
        try {
            Long userId = getUserIdFromRequest(request);
            if (userId == null) {
                return ApiResponse.error("用户未登录");
            }
            
            boolean isFavorited = favoriteService.isFavorited(userId, itemId, type);
            return ApiResponse.success(isFavorited);
        } catch (Exception e) {
            return ApiResponse.error("检查收藏状态失败：" + e.getMessage());
        }
    }

    /**
     * 从请求中获取用户ID
     */
    private Long getUserIdFromRequest(HttpServletRequest request) {
        // 这里应该从JWT token或session中获取用户ID
        // 暂时从请求头中获取，实际项目中需要根据认证方式调整
        String userIdStr = request.getHeader("User-Id");
        if (userIdStr != null) {
            try {
                return Long.parseLong(userIdStr);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}
