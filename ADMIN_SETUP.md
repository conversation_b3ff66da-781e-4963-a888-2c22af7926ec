# 管理员功能设置指南

## 1. 数据库初始化

在MySQL中执行以下SQL语句来创建管理员账户：

```sql
-- 创建管理员账户
INSERT INTO users (username, password, email, real_name, role, create_time, update_time) 
VALUES ('admin', 'admin123', '<EMAIL>', '系统管理员', 'ADMIN', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
role = 'ADMIN',
update_time = NOW();
```

或者直接执行提供的SQL文件：
```bash
mysql -u root -p xiaoyou < init_admin.sql
```

## 2. 管理员登录

1. 启动前后端服务
2. 访问管理员登录页面：`http://localhost:3000/admin/login`
3. 使用以下默认管理员账户登录：
   - 用户名：`admin`
   - 密码：`admin123`

## 3. 管理员功能

### 当前已实现功能：
- ✅ 管理员登录认证
- ✅ 管理员后台仪表板
- ✅ 基础统计数据展示
- ✅ 管理员权限验证

### 计划中的功能：
- 🚧 新闻管理（增删改查）
- 🚧 用户管理
- 🚧 系统设置
- 🚧 数据统计分析

## 4. 安全注意事项

⚠️ **重要提醒**：
1. 默认管理员密码为 `admin123`，请在生产环境中立即修改
2. 建议在生产环境中对密码进行加密存储
3. 考虑添加更多的安全验证机制（如验证码、双因子认证等）

## 5. 技术实现

### 后端实现：
- 在 `User` 实体中添加了 `role` 字段
- 创建了 `AdminController` 处理管理员登录
- 在 `UserService` 中添加了管理员登录验证逻辑

### 前端实现：
- 创建了 `AdminLogin.vue` 管理员登录页面
- 创建了 `AdminDashboard.vue` 管理员后台页面
- 更新了路由配置支持管理员页面
- 更新了 Vuex 状态管理支持管理员状态

## 6. API接口

### 管理员登录
- **URL**: `POST /api/admin/login`
- **参数**: 
  ```json
  {
    "username": "admin",
    "password": "admin123"
  }
  ```
- **响应**: 
  ```json
  {
    "code": 200,
    "message": "管理员登录成功",
    "data": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "realName": "系统管理员",
      "role": "ADMIN"
    }
  }
  ```

## 7. 页面访问

- 管理员登录：`http://localhost:3000/admin/login`
- 管理员后台：`http://localhost:3000/admin/dashboard`
- 普通用户登录：`http://localhost:3000/login`
- 首页：`http://localhost:3000/home`