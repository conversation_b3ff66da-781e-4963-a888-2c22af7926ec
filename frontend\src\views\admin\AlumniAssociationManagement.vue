<template>
  <div class="alumni-management">
    <h2 class="page-title">校友会管理</h2>
    
    <!-- 操作按钮 -->
    <div class="action-bar">
      <el-button type="primary" @click="showAddDialog" icon="el-icon-plus">
        添加校友会
      </el-button>
      <el-input
        v-model="searchKeyword"
        placeholder="搜索校友会名称"
        prefix-icon="el-icon-search"
        style="width: 300px; margin-left: 15px;"
        clearable
        @keyup.enter.native="handleSearch"
      />
      <el-button type="primary" @click="handleSearch" icon="el-icon-search">
        搜索
      </el-button>
    </div>
    
    <!-- 校友会列表 -->
    <el-table
      v-loading="loading"
      :data="associations"
      border
      style="width: 100%; margin-top: 20px;"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="校友会名称" min-width="150" />
      <el-table-column prop="president" label="会长" width="120" />
      <el-table-column prop="memberCount" label="成员数" width="100" />
      <el-table-column prop="address" label="地址" min-width="150" />
      <el-table-column label="成立时间" width="120">
        <template slot-scope="scope">
          {{ formatDate(scope.row.establishmentDate) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.active ? 'success' : 'info'">
            {{ scope.row.active ? '活跃' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="primary"
              @click="handleEdit(scope.row)"
              icon="el-icon-edit"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              :type="scope.row.active ? 'warning' : 'success'"
              @click="handleToggleStatus(scope.row)"
            >
              {{ scope.row.active ? '停用' : '启用' }}
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
    
    <!-- 添加/编辑校友会对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '添加校友会' : '编辑校友会'"
      :visible.sync="dialogVisible"
      width="650px"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-width="100px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="校友会名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入校友会名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会长" prop="president">
              <el-input v-model="form.president" placeholder="请输入会长姓名" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入地址" />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="成立时间" prop="establishmentDate">
              <el-date-picker
                v-model="form.establishmentDate"
                type="datetime"
                placeholder="选择成立时间"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="成员数量" prop="memberCount">
              <el-input-number
                v-model="form.memberCount"
                :min="0"
                :max="100000"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="Logo" prop="logoUrl">
          <el-upload
            class="logo-uploader"
            action="http://localhost:8080/api/upload/image"
            :show-file-list="false"
            :on-success="handleLogoSuccess"
            :before-upload="beforeLogoUpload"
          >
            <img v-if="form.logoUrl" :src="form.logoUrl" class="logo-image">
            <i v-else class="el-icon-plus logo-uploader-icon"></i>
          </el-upload>
          <div class="image-tip">建议尺寸: 200px × 200px，支持JPG、PNG格式</div>
        </el-form-item>
        
        <el-form-item label="校友会简介" prop="description">
          <el-input
            type="textarea"
            v-model="form.description"
            placeholder="请输入校友会简介"
            :rows="4"
          />
        </el-form-item>
        
        <el-form-item label="主要活动" prop="activities">
          <el-input
            type="textarea"
            v-model="form.activities"
            placeholder="请输入主要活动介绍"
            :rows="3"
          />
        </el-form-item>
        
        <el-form-item label="主要成就" prop="achievements">
          <el-input
            type="textarea"
            v-model="form.achievements"
            placeholder="请输入主要成就介绍"
            :rows="3"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="active">
          <el-switch
            v-model="form.active"
            active-text="活跃"
            inactive-text="停用"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'AlumniAssociationManagement',
  data() {
    return {
      associations: [],
      loading: false,
      searchKeyword: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogType: 'add', // 'add' or 'edit'
      submitting: false,
      form: {
        name: '',
        president: '',
        phone: '',
        email: '',
        address: '',
        logoUrl: '',
        establishmentDate: null,
        memberCount: 0,
        description: '',
        activities: '',
        achievements: '',
        active: true
      },
      rules: {
        name: [
          { required: true, message: '请输入校友会名称', trigger: 'blur' },
          { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
        ],
        president: [
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ],
        phone: [
          { max: 20, message: '长度不能超过20个字符', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ],
        address: [
          { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ],
        logoUrl: [
          { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ],
        description: [
          { max: 2000, message: '长度不能超过2000个字符', trigger: 'blur' }
        ],
        activities: [
          { max: 2000, message: '长度不能超过2000个字符', trigger: 'blur' }
        ],
        achievements: [
          { max: 2000, message: '长度不能超过2000个字符', trigger: 'blur' }
        ]
      },
      currentId: null
    }
  },
  mounted() {
    this.fetchAssociations()
  },
  methods: {
    async fetchAssociations() {
      try {
        this.loading = true
        const params = {
          page: this.currentPage - 1,
          size: this.pageSize
        }
        
        const url = this.searchKeyword
          ? `/api/alumni-associations?search=${encodeURIComponent(this.searchKeyword)}`
          : '/api/alumni-associations/admin/all'
        
        const response = await axios.get(url, { params })
        
        if (response.data && response.data.code === 200) {
          const pageData = response.data.data
          this.associations = pageData.content || []
          this.total = pageData.totalElements || 0
        } else {
          this.$message.error('获取校友会数据失败')
        }
      } catch (error) {
        console.error('获取校友会数据失败:', error)
        this.$message.error('获取校友会数据失败')
      } finally {
        this.loading = false
      }
    },
    
    handleSearch() {
      this.currentPage = 1
      this.fetchAssociations()
    },
    
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchAssociations()
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchAssociations()
    },
    
    showAddDialog() {
      this.dialogType = 'add'
      this.resetForm()
      this.dialogVisible = true
    },
    
    handleEdit(row) {
      this.dialogType = 'edit'
      this.currentId = row.id
      this.form = {
        name: row.name,
        president: row.president,
        phone: row.phone,
        email: row.email,
        address: row.address,
        logoUrl: row.logoUrl,
        establishmentDate: row.establishmentDate ? new Date(row.establishmentDate) : null,
        memberCount: row.memberCount || 0,
        description: row.description,
        activities: row.activities,
        achievements: row.achievements,
        active: row.active
      }
      this.dialogVisible = true
    },
    
    async handleToggleStatus(row) {
      try {
        const action = row.active ? 'deactivate' : 'activate'
        const response = await axios.put(`/api/alumni-associations/${row.id}/${action}`)
        
        if (response.data && response.data.code === 200) {
          this.$message.success(row.active ? '校友会已停用' : '校友会已启用')
          this.fetchAssociations()
        } else {
          this.$message.error('操作失败')
        }
      } catch (error) {
        console.error('操作失败:', error)
        this.$message.error('操作失败')
      }
    },
    
    async handleDelete(row) {
      try {
        await this.$confirm('此操作将永久删除该校友会, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await axios.delete(`/api/alumni-associations/${row.id}`)
        
        if (response.data && response.data.code === 200) {
          this.$message.success('删除成功')
          this.fetchAssociations()
        } else {
          this.$message.error('删除失败')
        }
      } catch (error) {
        if (error === 'cancel') {
          this.$message.info('已取消删除')
        } else {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }
    },
    
    resetForm() {
      this.form = {
        name: '',
        president: '',
        phone: '',
        email: '',
        address: '',
        logoUrl: '',
        establishmentDate: null,
        memberCount: 0,
        description: '',
        activities: '',
        achievements: '',
        active: true
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    
    beforeLogoUpload(file) {
      const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        this.$message.error('上传的文件必须是JPG或PNG格式!')
        return false
      }
      
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      
      return true
    },
    
    handleLogoSuccess(response, file) {
      if (response.code === 200) {
        // 处理后端返回的数据格式
        if (response.data && response.data.url) {
          this.form.logoUrl = response.data.url
          this.$message.success('Logo上传成功')
        } else {
          this.$message.error('Logo上传失败：返回数据格式错误')
        }
      } else {
        this.$message.error('Logo上传失败：' + (response.message || '未知错误'))
      }
    },
    
    async submitForm() {
      this.$refs.form.validate(async valid => {
        if (!valid) return
        
        try {
          this.submitting = true
          let response
          
          if (this.dialogType === 'add') {
            response = await axios.post('/api/alumni-associations', this.form)
          } else {
            response = await axios.put(`/api/alumni-associations/${this.currentId}`, this.form)
          }
          
          if (response.data && response.data.code === 200) {
            this.$message.success(this.dialogType === 'add' ? '添加成功' : '更新成功')
            this.dialogVisible = false
            this.fetchAssociations()
          } else {
            this.$message.error(this.dialogType === 'add' ? '添加失败' : '更新失败')
          }
        } catch (error) {
          console.error(this.dialogType === 'add' ? '添加失败:' : '更新失败:', error)
          this.$message.error(this.dialogType === 'add' ? '添加失败' : '更新失败')
        } finally {
          this.submitting = false
        }
      })
    },
    
    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style scoped>
.alumni-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-title {
  margin-bottom: 20px;
  font-size: 22px;
  color: #303133;
  font-weight: 500;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-form-item {
  margin-bottom: 18px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.operation-buttons .el-button {
  margin-left: 0;
  margin-right: 0;
}

/* Logo上传样式 */
.logo-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.logo-uploader .el-upload:hover {
  border-color: #409EFF;
}
.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.logo-image {
  width: 100px;
  height: 100px;
  display: block;
}
.image-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
