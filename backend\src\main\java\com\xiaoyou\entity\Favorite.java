package com.xiaoyou.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "favorites")
public class Favorite {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "item_type", nullable = false, length = 50)
    private String itemType; // news, activity, etc.
    
    @Column(name = "item_id", nullable = false)
    private Long itemId;
    
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    // 构造函数
    public Favorite() {}
    
    public Favorite(Long userId, String itemType, Long itemId) {
        this.userId = userId;
        this.itemType = itemType;
        this.itemId = itemId;
        this.createTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getItemType() {
        return itemType;
    }
    
    public void setItemType(String itemType) {
        this.itemType = itemType;
    }
    
    public Long getItemId() {
        return itemId;
    }
    
    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public String toString() {
        return "Favorite{" +
                "id=" + id +
                ", userId=" + userId +
                ", itemType='" + itemType + '\'' +
                ", itemId=" + itemId +
                ", createTime=" + createTime +
                '}';
    }
}