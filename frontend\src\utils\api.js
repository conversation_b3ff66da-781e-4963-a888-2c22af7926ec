import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'

const api = axios.create({
  baseURL: 'http://localhost:8080/api',
  timeout: 10000
})

// 请求拦截器 - 添加用户ID到请求头
api.interceptors.request.use(
  config => {
    // 获取当前登录用户信息
    const user = store.state.user
    if (user && user.id) {
      config.headers['User-Id'] = user.id
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('API Error:', error)
    const message = error.response && error.response.data && error.response.data.message
    Message.error(message || '网络错误')
    return Promise.reject(error)
  }
)

export default api
