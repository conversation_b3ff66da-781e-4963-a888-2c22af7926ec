package com.xiaoyou.repository;

import com.xiaoyou.entity.Activity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ActivityRepository extends JpaRepository<Activity, Long> {
    
    // 获取已发布的活动，按开始时间排序
    Page<Activity> findByPublishedTrueOrderByStartTimeDesc(Pageable pageable);
    
    // 根据标题搜索已发布的活动
    Page<Activity> findByTitleContainingIgnoreCaseAndPublishedTrueOrderByStartTimeDesc(String title, Pageable pageable);
    
    // 根据活动类型获取已发布的活动
    Page<Activity> findByTypeAndPublishedTrueOrderByStartTimeDesc(Activity.ActivityType type, Pageable pageable);
    
    // 根据活动状态获取已发布的活动
    Page<Activity> findByStatusAndPublishedTrueOrderByStartTimeDesc(Activity.ActivityStatus status, Pageable pageable);
    
    // 获取即将开始的活动（未来7天内）
    @Query("SELECT a FROM Activity a WHERE a.published = true AND a.startTime BETWEEN :now AND :future ORDER BY a.startTime ASC")
    List<Activity> findUpcomingActivities(@Param("now") LocalDateTime now, @Param("future") LocalDateTime future);
    
    // 增加浏览量
    @Modifying
    @Query("UPDATE Activity a SET a.viewCount = a.viewCount + 1 WHERE a.id = :id")
    int incrementViewCount(@Param("id") Long id);
    
    // 增加参与人数
    @Modifying
    @Query("UPDATE Activity a SET a.currentParticipants = a.currentParticipants + 1 WHERE a.id = :id")
    int incrementParticipants(@Param("id") Long id);
    
    // 减少参与人数
    @Modifying
    @Query("UPDATE Activity a SET a.currentParticipants = a.currentParticipants - 1 WHERE a.id = :id AND a.currentParticipants > 0")
    int decrementParticipants(@Param("id") Long id);
    
    // 获取热门活动（按浏览量排序）
    @Query("SELECT a FROM Activity a WHERE a.published = true ORDER BY a.viewCount DESC")
    List<Activity> findPopularActivities(Pageable pageable);
}