package com.xiaoyou.controller;

import com.xiaoyou.dto.ApiResponse;
import com.xiaoyou.entity.PostLike;
import com.xiaoyou.service.PostLikeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/api/post-likes")
@Validated
@CrossOrigin(origins = "http://localhost:8081")
public class PostLikeController {
    
    @Autowired
    private PostLikeService postLikeService;
    
    // 点赞帖子
    @PostMapping("/posts/{postId}/like")
    public ApiResponse<Boolean> likePost(@PathVariable Long postId, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.error(401, "请先登录");
            }
            
            boolean success = postLikeService.likePost(postId, userId);
            if (success) {
                return ApiResponse.success("点赞成功", true);
            } else {
                return ApiResponse.error(400, "您已经点赞过了");
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "点赞失败: " + e.getMessage());
        }
    }
    
    // 取消点赞
    @DeleteMapping("/posts/{postId}/like")
    public ApiResponse<Boolean> unlikePost(@PathVariable Long postId, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.error(401, "请先登录");
            }
            
            boolean success = postLikeService.unlikePost(postId, userId);
            if (success) {
                return ApiResponse.success("取消点赞成功", true);
            } else {
                return ApiResponse.error(400, "您还没有点赞过");
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "取消点赞失败: " + e.getMessage());
        }
    }
    
    // 检查用户是否已点赞帖子
    @GetMapping("/posts/{postId}/liked")
    public ApiResponse<Boolean> isPostLikedByUser(@PathVariable Long postId, HttpServletRequest request) {
        try {
            Long userId = getCurrentUserId(request);
            if (userId == null) {
                return ApiResponse.success("未登录", false);
            }
            
            boolean liked = postLikeService.isPostLikedByUser(postId, userId);
            return ApiResponse.success("查询成功", liked);
        } catch (Exception e) {
            return ApiResponse.error(500, "查询失败: " + e.getMessage());
        }
    }
    
    // 获取帖子的点赞列表
    @GetMapping("/posts/{postId}")
    public ApiResponse<List<PostLike>> getPostLikes(@PathVariable Long postId) {
        try {
            List<PostLike> likes = postLikeService.getPostLikes(postId);
            return ApiResponse.success("获取点赞列表成功", likes);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取点赞列表失败: " + e.getMessage());
        }
    }
    
    // 获取用户的点赞列表
    @GetMapping("/users/{userId}")
    public ApiResponse<List<PostLike>> getUserLikes(@PathVariable Long userId) {
        try {
            List<PostLike> likes = postLikeService.getUserLikes(userId);
            return ApiResponse.success("获取用户点赞列表成功", likes);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取用户点赞列表失败: " + e.getMessage());
        }
    }
    
    // 统计帖子点赞数
    @GetMapping("/posts/{postId}/count")
    public ApiResponse<Long> countPostLikes(@PathVariable Long postId) {
        try {
            Long count = postLikeService.countPostLikes(postId);
            return ApiResponse.success("获取点赞数成功", count);
        } catch (Exception e) {
            return ApiResponse.error(500, "获取点赞数失败: " + e.getMessage());
        }
    }
    
    // 辅助方法：获取当前用户ID
    private Long getCurrentUserId(HttpServletRequest request) {
        String userIdHeader = request.getHeader("User-Id");
        if (userIdHeader != null) {
            try {
                return Long.parseLong(userIdHeader);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}