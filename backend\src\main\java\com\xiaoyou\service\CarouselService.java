package com.xiaoyou.service;

import com.xiaoyou.entity.Carousel;
import com.xiaoyou.repository.CarouselRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CarouselService {
    
    @Autowired
    private CarouselRepository carouselRepository;
    
    /**
     * 获取所有轮播图
     */
    public List<Carousel> getAllCarousels() {
        return carouselRepository.findAllOrderBySortOrder();
    }
    
    /**
     * 获取所有启用的轮播图
     */
    public List<Carousel> getActiveCarousels() {
        return carouselRepository.findActiveCarouselsOrderBySortOrder();
    }
    
    /**
     * 根据ID获取轮播图
     */
    public Optional<Carousel> getCarouselById(Long id) {
        return carouselRepository.findById(id);
    }
    
    /**
     * 创建轮播图
     */
    public Carousel createCarousel(Carousel carousel) {
        return carouselRepository.save(carousel);
    }
    
    /**
     * 更新轮播图
     */
    public Carousel updateCarousel(Long id, Carousel carouselDetails) {
        Optional<Carousel> optionalCarousel = carouselRepository.findById(id);
        if (optionalCarousel.isPresent()) {
            Carousel carousel = optionalCarousel.get();
            carousel.setTitle(carouselDetails.getTitle());
            carousel.setImageUrl(carouselDetails.getImageUrl());
            carousel.setLinkUrl(carouselDetails.getLinkUrl());
            carousel.setDescription(carouselDetails.getDescription());
            carousel.setSortOrder(carouselDetails.getSortOrder());
            carousel.setIsActive(carouselDetails.getIsActive());
            return carouselRepository.save(carousel);
        }
        throw new RuntimeException("轮播图不存在，ID: " + id);
    }
    
    /**
     * 删除轮播图
     */
    public void deleteCarousel(Long id) {
        if (carouselRepository.existsById(id)) {
            carouselRepository.deleteById(id);
        } else {
            throw new RuntimeException("轮播图不存在，ID: " + id);
        }
    }
    
    /**
     * 切换轮播图状态
     */
    public Carousel toggleCarouselStatus(Long id) {
        Optional<Carousel> optionalCarousel = carouselRepository.findById(id);
        if (optionalCarousel.isPresent()) {
            Carousel carousel = optionalCarousel.get();
            carousel.setIsActive(!carousel.getIsActive());
            return carouselRepository.save(carousel);
        }
        throw new RuntimeException("轮播图不存在，ID: " + id);
    }
}