<template>
  <div class="layout-wrapper">
    <div class="layout-content">
      <!-- 头部 -->
      <el-header style="background-color: #545c64; color: white; line-height: 60px; height: 60px;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <h1 style="margin: 0; cursor: pointer;" @click="goHome">校友信息管理系统</h1>
          <div v-if="user" class="user-section">
            <div class="user-info">
              <img 
                :src="getAvatarUrl(user.avatar)" 
                alt="用户头像" 
                class="user-avatar"
                @error="handleAvatarError"
              />
              <span class="user-name">{{ user.realName || user.username }}</span>
            </div>
            <el-dropdown @command="handleCommand" trigger="hover">
              <span class="el-dropdown-link">
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="profile">
                  <i class="el-icon-user"></i>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <i class="el-icon-setting"></i>
                  账户设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <i class="el-icon-switch-button"></i>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div v-else class="auth-buttons">
            <router-link to="/login">
              <el-button type="primary" size="small">登录</el-button>
            </router-link>
            <router-link to="/register">
              <el-button type="success" size="small" style="margin-left: 10px;">注册</el-button>
            </router-link>
            <router-link to="/admin/login">
              <el-button type="warning" size="small" style="margin-left: 10px;">管理员</el-button>
            </router-link>
          </div>
        </div>
      </el-header>
      
      <!-- 导航菜单 -->
      <div class="nav-container">
        <el-menu 
          :default-active="currentRoute" 
          class="nav-menu" 
          mode="horizontal"
          background-color="#545c64"
          text-color="#fff"
          active-text-color="#ffd04b"
          @select="handleMenuSelect"
        >
          <el-menu-item index="home">首页</el-menu-item>
          <el-menu-item index="news">新闻</el-menu-item>
          <el-menu-item index="alumni-association">校友会</el-menu-item>
          <el-menu-item index="alumni-activities">校友活动</el-menu-item>
          <el-menu-item index="jobs">招聘信息</el-menu-item>
          <el-menu-item index="forum">论坛</el-menu-item>
          <el-menu-item index="donations">校友捐赠</el-menu-item>
        </el-menu>
      </div>
      
      <!-- 主要内容 -->
      <div class="main-content">
        <slot></slot>
      </div>
    </div>
    
    <!-- 底部固定在页面底部 -->
    <div class="layout-footer">
      <div style="background-color: #2c3e50; color: white; text-align: center; padding: 20px 0;">
        <p style="margin: 0;">&copy; 2024 校友信息管理系统. All rights reserved.</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Layout',
  data() {
    return {
      defaultAvatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY3RkEiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNSIgcj0iNiIgZmlsbD0iIzhDOTNEIi8+CjxwYXRoIGQ9Ik0xMCAzMEMxMCAyNSAxNCAyMCAyMCAyMEMyNiAyMCAzMCAyNSAzMCAzMCIgZmlsbD0iIzhDOTNEIi8+Cjwvc3ZnPgo='
    }
  },
  computed: {
    user() {
      return this.$store.state.user
    },
    currentRoute() {
      const path = this.$route.path
      if (path === '/' || path === '/home') return 'home'
      if (path.startsWith('/news')) return 'news'
      if (path.startsWith('/alumni-association')) return 'alumni-association'
      if (path.startsWith('/activities')) return 'alumni-activities'
      if (path.startsWith('/jobs')) return 'jobs'
      if (path.startsWith('/forum')) return 'forum'
      return 'home'
    }
  },
  methods: {
    getAvatarUrl(avatar) {
      if (!avatar) return this.defaultAvatar
      if (avatar.startsWith('http')) return avatar
      if (avatar.startsWith('/uploads') || avatar.startsWith('/images')) {
        return `http://localhost:8080${avatar}`
      }
      return avatar
    },
    
    handleAvatarError(event) {
      event.target.src = this.defaultAvatar
    },
    
    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$router.push('/profile')
          break
        case 'settings':
          this.$router.push('/profile')
          break
        case 'logout':
          this.logout()
          break
      }
    },
    logout() {
      this.$store.dispatch('logout')
      this.$message.success('退出登录成功')
      this.$router.push('/login')
    },
    goHome() {
      this.$router.push('/home')
    },
    handleMenuSelect(index) {
      switch (index) {
        case 'home':
          this.$router.push('/home')
          break
        case 'news':
          this.$router.push('/news')
          break
        case 'alumni-association':
          this.$router.push('/alumni-association')
          break
        case 'alumni-activities':
          this.$router.push('/activities')
          break
        case 'jobs':
          this.$router.push('/jobs')
          break
        case 'forum':
          this.$router.push('/forum')
          break
        case 'donations':
          this.$message.info('校友捐赠功能即将上线')
          break
        default:
          break
      }
    }
  }
}
</script>

<style scoped>
/* 整体布局 - 使用 Flexbox 确保 footer 在底部 */
.layout-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  min-height: calc(100vh - 180px); /* 减去头部和导航的高度 */
}

.layout-footer {
  margin-top: auto;
  width: 100%;
}

/* 导航容器样式 */
.nav-container {
  background-color: #545c64;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 60px;
}

.nav-menu {
  border: none !important;
  display: flex;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
  width: auto;
}

.nav-menu .el-menu-item {
  border-bottom: none !important;
  padding: 0 20px;
  height: 60px;
  line-height: 60px;
}

/* 用户信息样式 */
.user-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: border-color 0.3s;
}

.user-avatar:hover {
  border-color: rgba(255, 255, 255, 0.8);
}

.user-name {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.el-dropdown-link {
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.el-dropdown-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.auth-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 下拉菜单样式 */
.el-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: none;
  padding: 8px 0;
}

.el-dropdown-menu .el-dropdown-menu__item {
  padding: 10px 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.el-dropdown-menu .el-dropdown-menu__item:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.el-dropdown-menu .el-dropdown-menu__item i {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-menu .el-menu-item {
    padding: 0 15px;
    font-size: 14px;
  }
  
  .user-name {
    display: none;
  }
  
  .auth-buttons {
    gap: 5px;
  }
  
  .auth-buttons .el-button {
    padding: 7px 10px;
    font-size: 12px;
  }
}
</style>