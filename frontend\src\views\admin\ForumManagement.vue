<template>
  <div class="forum-management">
    <div class="page-header">
      <h2>论坛管理</h2>
      <p>管理论坛帖子、评论和用户互动</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon post-icon">
            <i class="el-icon-document"></i>
          </div>
          <div class="stat-info">
            <h3>{{ totalPosts }}</h3>
            <p>总帖子数</p>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon comment-icon">
            <i class="el-icon-chat-line-square"></i>
          </div>
          <div class="stat-info">
            <h3>{{ totalComments }}</h3>
            <p>总评论数</p>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon like-icon">
            <i class="el-icon-thumb"></i>
          </div>
          <div class="stat-info">
            <h3>{{ totalLikes }}</h3>
            <p>总点赞数</p>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon today-icon">
            <i class="el-icon-date"></i>
          </div>
          <div class="stat-info">
            <h3>{{ todayPosts }}</h3>
            <p>今日新帖</p>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <div class="management-actions">
      <el-button type="primary" @click="refreshData">
        <i class="el-icon-refresh"></i>
        刷新数据
      </el-button>
      <el-button @click="exportData">
        <i class="el-icon-download"></i>
        导出数据
      </el-button>
    </div>

    <!-- 帖子管理表格 -->
    <div class="table-container">
      <el-table 
        :data="postList" 
        v-loading="loading"
        style="width: 100%"
        stripe
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="120">
          <template slot-scope="scope">
            <el-tag :type="getCategoryType(scope.row.category)">
              {{ getCategoryName(scope.row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="author" label="作者" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="viewCount" label="浏览" width="80" />
        <el-table-column prop="likeCount" label="点赞" width="80" />
        <el-table-column prop="commentCount" label="评论" width="80" />
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="viewPost(scope.row)">查看</el-button>
            <el-button size="mini" type="warning" @click="togglePin(scope.row)">
              {{ scope.row.pinned ? '取消置顶' : '置顶' }}
            </el-button>
            <el-button size="mini" type="danger" @click="deletePost(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script>
import api from '@/utils/api'

export default {
  name: 'ForumManagement',
  data() {
    return {
      loading: false,
      totalPosts: 0,
      totalComments: 0,
      totalLikes: 0,
      todayPosts: 0,
      postList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      this.loading = true
      try {
        // 加载统计数据
        await this.loadStats()
        // 加载帖子列表
        await this.loadPostList()
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    async loadStats() {
      try {
        const response = await api.get('/posts/admin/stats')
        if (response.code === 200) {
          const stats = response.data
          this.totalPosts = stats.totalPosts || 0
          this.totalComments = stats.totalComments || 0
          this.totalLikes = stats.totalLikes || 0
          this.todayPosts = stats.todayPosts || 0
        }
      } catch (error) {
        // 使用模拟数据
        this.totalPosts = 156
        this.totalComments = 423
        this.totalLikes = 892
        this.todayPosts = 12
      }
    },

    async loadPostList() {
      try {
        const response = await api.get('/posts/admin/all', {
          params: {
            page: this.currentPage - 1,
            size: this.pageSize
          }
        })
        
        if (response.code === 200) {
          this.postList = response.data.content || []
          this.total = response.data.totalElements || 0
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('获取帖子列表失败:', error)
        // 使用模拟数据
        this.postList = [
          {
            id: 1,
            title: '欢迎来到校友论坛',
            category: 'GENERAL',
            author: '管理员',
            status: 'PUBLISHED',
            viewCount: 156,
            likeCount: 23,
            commentCount: 8,
            pinned: true,
            createdAt: new Date().toISOString()
          }
        ]
        this.total = 1
      }
    },

    refreshData() {
      this.loadData()
    },

    exportData() {
      this.$message.info('导出功能开发中...')
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadPostList()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadPostList()
    },

    viewPost(post) {
      // 打开新窗口查看帖子
      const routeData = this.$router.resolve({
        path: `/forum/post/${post.id}`
      })
      window.open(routeData.href, '_blank')
    },

    async togglePin(post) {
      try {
        const response = await api.put(`/posts/admin/${post.id}/toggle-pin`)
        
        if (response.code === 200) {
          this.$message.success(post.pinned ? '取消置顶成功' : '置顶成功')
          this.loadPostList()
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('操作失败:', error)
        this.$message.error('操作失败')
      }
    },

    deletePost(post) {
      this.$confirm(`确定要删除帖子"${post.title}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await api.delete(`/posts/${post.id}`)
          
          if (response.code === 200) {
            this.$message.success('帖子删除成功')
            this.loadPostList()
          } else {
            this.$message.error(response.message)
          }
        } catch (error) {
          console.error('删除帖子失败:', error)
          this.$message.error('删除帖子失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    getCategoryName(category) {
      const categoryMap = {
        'GENERAL': '综合讨论',
        'ACADEMIC': '学术交流',
        'CAREER': '职业发展',
        'LIFE': '生活分享',
        'TECHNOLOGY': '技术讨论',
        'ALUMNI': '校友动态',
        'QUESTION': '问答求助',
        'ANNOUNCEMENT': '公告通知'
      }
      return categoryMap[category] || category
    },

    getCategoryType(category) {
      const typeMap = {
        'GENERAL': '',
        'ACADEMIC': 'success',
        'CAREER': 'warning',
        'LIFE': 'info',
        'TECHNOLOGY': 'primary',
        'ALUMNI': 'danger',
        'QUESTION': 'warning',
        'ANNOUNCEMENT': 'info'
      }
      return typeMap[category] || ''
    },

    getStatusName(status) {
      const statusMap = {
        'DRAFT': '草稿',
        'PUBLISHED': '已发布',
        'HIDDEN': '已隐藏',
        'DELETED': '已删除'
      }
      return statusMap[status] || status
    },

    getStatusType(status) {
      const typeMap = {
        'DRAFT': 'info',
        'PUBLISHED': 'success',
        'HIDDEN': 'warning',
        'DELETED': 'danger'
      }
      return typeMap[status] || ''
    },

    formatDateTime(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.forum-management {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #64748b;
  font-size: 14px;
  margin: 0;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.stat-icon i {
  font-size: 24px;
  color: white;
}

.post-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.comment-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.like-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.today-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
  font-size: 24px;
  color: #2c3e50;
  margin: 0 0 4px 0;
  font-weight: 600;
}

.stat-info p {
  color: #64748b;
  margin: 0;
  font-size: 14px;
}

.management-actions {
  margin-bottom: 20px;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
}

.el-table .el-button--mini {
  padding: 5px 8px;
  font-size: 12px;
}
</style>