<template>
  <div class="activity-management">
    <h2 class="page-title">校友活动管理</h2>
    
    <!-- 操作按钮 -->
    <div class="action-bar">
      <el-button type="primary" @click="showAddDialog" icon="el-icon-plus">
        添加活动
      </el-button>
      <el-input
        v-model="searchKeyword"
        placeholder="搜索活动标题"
        prefix-icon="el-icon-search"
        style="width: 300px; margin-left: 15px;"
        clearable
        @keyup.enter.native="handleSearch"
      />
      <el-button type="primary" @click="handleSearch" icon="el-icon-search">
        搜索
      </el-button>
      <el-button type="success" @click="updateActivityStatus" icon="el-icon-refresh">
        更新状态
      </el-button>
    </div>
    
    <!-- 活动列表 -->
    <el-table
      v-loading="loading"
      :data="activities"
      border
      style="width: 100%; margin-top: 20px;"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="title" label="活动标题" min-width="200" />
      <el-table-column prop="type" label="类型" width="100">
        <template slot-scope="scope">
          {{ getTypeText(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="organizer" label="组织者" width="120" />
      <el-table-column prop="location" label="地点" min-width="150" />
      <el-table-column label="开始时间" width="150">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.startTime) }}
        </template>
      </el-table-column>
      <el-table-column label="参与人数" width="100">
        <template slot-scope="scope">
          {{ scope.row.currentParticipants }}/{{ scope.row.maxParticipants }}
        </template>
      </el-table-column>
      <el-table-column label="费用" width="80">
        <template slot-scope="scope">
          <span v-if="scope.row.fee > 0">¥{{ scope.row.fee }}</span>
          <span v-else>免费</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发布状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.published ? 'success' : 'info'">
            {{ scope.row.published ? '已发布' : '草稿' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="primary"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              :type="scope.row.published ? 'warning' : 'success'"
              @click="handleTogglePublish(scope.row)"
            >
              {{ scope.row.published ? '取消发布' : '发布' }}
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
    
    <!-- 添加/编辑活动对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '添加活动' : '编辑活动'"
      :visible.sync="dialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-width="100px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入活动标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织者" prop="organizer">
              <el-input v-model="form.organizer" placeholder="请输入组织者" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="活动类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择活动类型" style="width: 100%;">
                <el-option label="会议" value="CONFERENCE"></el-option>
                <el-option label="研讨会" value="SEMINAR"></el-option>
                <el-option label="联谊活动" value="NETWORKING"></el-option>
                <el-option label="公益活动" value="CHARITY"></el-option>
                <el-option label="体育活动" value="SPORTS"></el-option>
                <el-option label="文化活动" value="CULTURAL"></el-option>
                <el-option label="其他" value="OTHER"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择活动状态" style="width: 100%;">
                <el-option label="即将开始" value="UPCOMING"></el-option>
                <el-option label="进行中" value="ONGOING"></el-option>
                <el-option label="已结束" value="COMPLETED"></el-option>
                <el-option label="已取消" value="CANCELLED"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="活动地点" prop="location">
          <el-input v-model="form.location" placeholder="请输入活动地点" />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最大人数" prop="maxParticipants">
              <el-input-number
                v-model="form.maxParticipants"
                :min="1"
                :max="10000"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动费用" prop="fee">
              <el-input-number
                v-model="form.fee"
                :min="0"
                :precision="2"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="活动图片" prop="imageUrl">
          <el-upload
            class="image-uploader"
            action="http://localhost:8080/api/upload/image"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="form.imageUrl" :src="form.imageUrl" class="uploaded-image">
            <i v-else class="el-icon-plus image-uploader-icon"></i>
          </el-upload>
          <div class="image-tip">建议尺寸: 800px × 400px，支持JPG、PNG格式</div>
        </el-form-item>
        
        <el-form-item label="活动简介" prop="summary">
          <el-input
            type="textarea"
            v-model="form.summary"
            placeholder="请输入活动简介"
            :rows="3"
          />
        </el-form-item>
        
        <el-form-item label="活动详情" prop="description">
          <el-input
            type="textarea"
            v-model="form.description"
            placeholder="请输入活动详情"
            :rows="6"
          />
        </el-form-item>
        
        <el-form-item label="发布状态" prop="published">
          <el-switch
            v-model="form.published"
            active-text="发布"
            inactive-text="草稿"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'ActivityManagement',
  data() {
    return {
      activities: [],
      loading: false,
      searchKeyword: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      dialogType: 'add',
      submitting: false,
      form: {
        title: '',
        description: '',
        summary: '',
        organizer: '',
        imageUrl: '',
        location: '',
        startTime: null,
        endTime: null,
        maxParticipants: 50,
        fee: 0,
        status: 'UPCOMING',
        type: 'OTHER',
        published: false
      },
      rules: {
        title: [
          { required: true, message: '请输入活动标题', trigger: 'blur' },
          { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入活动详情', trigger: 'blur' }
        ],
        organizer: [
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ],
        location: [
          { required: true, message: '请输入活动地点', trigger: 'blur' },
          { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        maxParticipants: [
          { required: true, message: '请输入最大参与人数', trigger: 'blur' }
        ],
        summary: [
          { max: 500, message: '长度不能超过500个字符', trigger: 'blur' }
        ],
        imageUrl: [
          { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      },
      currentId: null
    }
  },
  mounted() {
    this.fetchActivities()
  },
  methods: {
    async fetchActivities() {
      try {
        this.loading = true
        const params = {
          page: this.currentPage - 1,
          size: this.pageSize
        }
        
        const url = this.searchKeyword
          ? `/api/activities?search=${encodeURIComponent(this.searchKeyword)}`
          : '/api/activities/admin/all'
        
        const response = await axios.get(url, { params })
        
        if (response.data && response.data.code === 200) {
          const pageData = response.data.data
          this.activities = pageData.content || []
          this.total = pageData.totalElements || 0
        } else {
          this.$message.error('获取活动数据失败')
        }
      } catch (error) {
        console.error('获取活动数据失败:', error)
        this.$message.error('获取活动数据失败')
      } finally {
        this.loading = false
      }
    },
    
    handleSearch() {
      this.currentPage = 1
      this.fetchActivities()
    },
    
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.fetchActivities()
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchActivities()
    },
    
    showAddDialog() {
      this.dialogType = 'add'
      this.resetForm()
      this.dialogVisible = true
    },
    
    handleEdit(row) {
      this.dialogType = 'edit'
      this.currentId = row.id
      this.form = {
        title: row.title,
        description: row.description,
        summary: row.summary,
        organizer: row.organizer,
        imageUrl: row.imageUrl,
        location: row.location,
        startTime: row.startTime ? new Date(row.startTime) : null,
        endTime: row.endTime ? new Date(row.endTime) : null,
        maxParticipants: row.maxParticipants || 50,
        fee: row.fee || 0,
        status: row.status,
        type: row.type,
        published: row.published
      }
      this.dialogVisible = true
    },
    
    async handleTogglePublish(row) {
      try {
        const newStatus = !row.published
        const response = await axios.put(`/api/activities/${row.id}`, {
          ...row,
          published: newStatus
        })
        
        if (response.data && response.data.code === 200) {
          this.$message.success(newStatus ? '活动已发布' : '活动已取消发布')
          this.fetchActivities()
        } else {
          this.$message.error('操作失败')
        }
      } catch (error) {
        console.error('操作失败:', error)
        this.$message.error('操作失败')
      }
    },
    
    async handleDelete(row) {
      try {
        await this.$confirm('此操作将永久删除该活动, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await axios.delete(`/api/activities/${row.id}`)
        
        if (response.data && response.data.code === 200) {
          this.$message.success('删除成功')
          this.fetchActivities()
        } else {
          this.$message.error('删除失败')
        }
      } catch (error) {
        if (error === 'cancel') {
          this.$message.info('已取消删除')
        } else {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }
    },
    
    async updateActivityStatus() {
      try {
        const response = await axios.post('/api/activities/admin/update-status')
        
        if (response.data && response.data.code === 200) {
          this.$message.success('活动状态更新成功')
          this.fetchActivities()
        } else {
          this.$message.error('更新失败')
        }
      } catch (error) {
        console.error('更新失败:', error)
        this.$message.error('更新失败')
      }
    },
    
    resetForm() {
      this.form = {
        title: '',
        description: '',
        summary: '',
        organizer: '',
        imageUrl: '',
        location: '',
        startTime: null,
        endTime: null,
        maxParticipants: 50,
        fee: 0,
        status: 'UPCOMING',
        type: 'OTHER',
        published: false
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    
    beforeImageUpload(file) {
      const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      
      if (!isImage) {
        this.$message.error('上传的文件必须是JPG或PNG格式!')
        return false
      }
      
      if (!isLt5M) {
        this.$message.error('图片大小不能超过 5MB!')
        return false
      }
      
      return true
    },
    
    handleImageSuccess(response, file) {
      if (response.code === 200) {
        if (response.data && response.data.url) {
          this.form.imageUrl = response.data.url
          this.$message.success('图片上传成功')
        } else {
          this.$message.error('图片上传失败：返回数据格式错误')
        }
      } else {
        this.$message.error('图片上传失败：' + (response.message || '未知错误'))
      }
    },
    
    async submitForm() {
      this.$refs.form.validate(async valid => {
        if (!valid) return
        
        // 验证时间
        if (this.form.startTime >= this.form.endTime) {
          this.$message.error('开始时间必须早于结束时间')
          return
        }
        
        try {
          this.submitting = true
          let response
          
          if (this.dialogType === 'add') {
            response = await axios.post('/api/activities', this.form)
          } else {
            response = await axios.put(`/api/activities/${this.currentId}`, this.form)
          }
          
          if (response.data && response.data.code === 200) {
            this.$message.success(this.dialogType === 'add' ? '添加成功' : '更新成功')
            this.dialogVisible = false
            this.fetchActivities()
          } else {
            this.$message.error(this.dialogType === 'add' ? '添加失败' : '更新失败')
          }
        } catch (error) {
          console.error(this.dialogType === 'add' ? '添加失败:' : '更新失败:', error)
          this.$message.error(this.dialogType === 'add' ? '添加失败' : '更新失败')
        } finally {
          this.submitting = false
        }
      })
    },
    
    formatDateTime(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    
    getTypeText(type) {
      const typeMap = {
        'CONFERENCE': '会议',
        'SEMINAR': '研讨会',
        'NETWORKING': '联谊活动',
        'CHARITY': '公益活动',
        'SPORTS': '体育活动',
        'CULTURAL': '文化活动',
        'OTHER': '其他'
      }
      return typeMap[type] || type
    },
    
    getStatusText(status) {
      const statusMap = {
        'UPCOMING': '即将开始',
        'ONGOING': '进行中',
        'COMPLETED': '已结束',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || status
    },
    
    getStatusTagType(status) {
      const typeMap = {
        'UPCOMING': 'primary',
        'ONGOING': 'success',
        'COMPLETED': 'info',
        'CANCELLED': 'danger'
      }
      return typeMap[status] || 'info'
    }
  }
}
</script>

<style scoped>
.activity-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-title {
  margin-bottom: 20px;
  font-size: 22px;
  color: #303133;
  font-weight: 500;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-form-item {
  margin-bottom: 18px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.operation-buttons .el-button {
  margin-left: 0;
  margin-right: 0;
}

.image-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 200px;
  height: 100px;
}

.image-uploader .el-upload:hover {
  border-color: #409EFF;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 200px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.uploaded-image {
  width: 200px;
  height: 100px;
  display: block;
  object-fit: cover;
}

.image-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>