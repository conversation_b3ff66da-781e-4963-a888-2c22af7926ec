{"permissions": {"allow": ["<PERSON><PERSON>(mkdir -p backend frontend)", "Bash(mkdir -p backend/src/main/java/com/xia<PERSON><PERSON>/{controller,service,repository,entity,config,dto} backend/src/main/resources backend/src/test/java)", "Bash(mkdir -p frontend/{src/{components,views,router,store,assets/{css,images},utils},public})", "Bash(npm install)", "Bash(npm ls:*)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(find:*)"], "deny": []}}