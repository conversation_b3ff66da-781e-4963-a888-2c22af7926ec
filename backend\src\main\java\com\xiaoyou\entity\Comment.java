package com.xiaoyou.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "comments")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Comment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "评论内容不能为空")
    @Size(max = 1000, message = "评论内容不能超过1000个字符")
    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "post_id", nullable = false)
    @JsonIgnoreProperties({"comments", "likes", "content"})
    private Post post;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "author_id", nullable = false)
    @JsonIgnoreProperties({"password", "email", "phone", "posts", "comments"})
    private User author;
    
    // 父评论ID，用于实现多级评论
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    @JsonIgnoreProperties({"post", "author", "children", "parent"})
    private Comment parent;
    
    // 子评论列表
    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnoreProperties({"parent"})
    private List<Comment> children;
    
    @Column(name = "like_count", nullable = false)
    private Integer likeCount = 0;
    
    @Column(nullable = false)
    private String status = "PUBLISHED";
    
    @Column(name = "create_time", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    public enum CommentStatus {
        PUBLISHED("已发布"),
        HIDDEN("已隐藏"),
        DELETED("已删除");
        
        private final String displayName;
        
        CommentStatus(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public static CommentStatus fromString(String status) {
            if (status == null) return PUBLISHED;
            try {
                return CommentStatus.valueOf(status.toUpperCase());
            } catch (IllegalArgumentException e) {
                return PUBLISHED;
            }
        }
    }
    
    @PrePersist
    protected void onCreate() {
        createTime = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public Post getPost() {
        return post;
    }
    
    public void setPost(Post post) {
        this.post = post;
    }
    
    public User getAuthor() {
        return author;
    }
    
    public void setAuthor(User author) {
        this.author = author;
    }
    
    public Comment getParent() {
        return parent;
    }
    
    public void setParent(Comment parent) {
        this.parent = parent;
    }
    
    public List<Comment> getChildren() {
        return children;
    }
    
    public void setChildren(List<Comment> children) {
        this.children = children;
    }
    
    public Integer getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    // 辅助方法：获取枚举状态
    public CommentStatus getStatusEnum() {
        return CommentStatus.fromString(this.status);
    }
    
    // 辅助方法：设置枚举状态
    public void setStatusEnum(CommentStatus status) {
        this.status = status != null ? status.name() : "PUBLISHED";
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}