<template>
  <div class="comment-item">
    <div class="comment-header">
      <div class="author-info">
        <el-avatar :size="32" :src="comment.author && comment.author.avatar" icon="el-icon-user-solid"></el-avatar>
        <div class="author-details">
          <div class="author-name">{{ (comment.author && comment.author.name) || '匿名用户' }}</div>
          <div class="comment-time">{{ formatTime(comment.createdAt) }}</div>
        </div>
      </div>
      
      <div class="comment-actions" v-if="canDeleteComment">
        <el-dropdown @command="handleAction">
          <el-button type="text" icon="el-icon-more" size="mini"></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="delete">删除</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

    <div class="comment-content">
      <div class="comment-text" v-html="renderedContent"></div>
      
      <!-- 评论图片 -->
      <div class="comment-images" v-if="comment.images && comment.images.length > 0">
        <el-image
          v-for="(image, index) in comment.images"
          :key="index"
          :src="image"
          :preview-src-list="comment.images"
          class="comment-image"
          fit="cover"
        />
      </div>
    </div>

    <div class="comment-footer">
      <div class="comment-stats">
        <span class="like-btn" :class="{ 'liked': comment.isLiked }" @click="toggleLike">
          <i class="el-icon-thumb"></i>
          {{ comment.likeCount || 0 }}
        </span>
      </div>
      
      <div class="comment-operations">
        <el-button type="text" size="mini" @click="$emit('reply', comment)">
          回复
        </el-button>
      </div>
    </div>

    <!-- 回复列表 -->
    <div class="replies-section" v-if="comment.replies && comment.replies.length > 0">
      <div class="replies-header">
        <span>{{ comment.replies.length }} 条回复</span>
      </div>
      <div class="replies-list">
        <CommentItem
          v-for="reply in comment.replies"
          :key="reply.id"
          :comment="reply"
          :is-reply="true"
          @reply="$emit('reply', $event)"
          @delete="$emit('delete', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script>
import api from '@/utils/api'
import marked from 'marked'

export default {
  name: 'CommentItem',
  props: {
    comment: {
      type: Object,
      required: true
    },
    isReply: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    isLoggedIn() {
      return this.$store.state.user && this.$store.state.user.id
    },
    canDeleteComment() {
      if (!this.isLoggedIn) return false
      return (this.comment.author && this.comment.author.id) === this.$store.state.user.id || this.$store.state.user.role === 'ADMIN'
    },
    renderedContent() {
      if (!this.comment.content) return ''
      try {
        // 简单的文本处理，支持基本的markdown
        let content = this.comment.content
        // 处理@用户
        content = content.replace(/@(\w+)/g, '<span class="mention">@$1</span>')
        // 处理链接
        content = content.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>')
        return content
      } catch (error) {
        return this.comment.content
      }
    }
  },
  methods: {
    async toggleLike() {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录')
        return
      }

      try {
        let response
        if (this.comment.isLiked) {
          response = await api.delete(`/comment-likes/${this.comment.id}/like`)
        } else {
          response = await api.post(`/comment-likes/${this.comment.id}/like`)
        }

        if (response.code === 200) {
          this.comment.isLiked = !this.comment.isLiked
          this.comment.likeCount += this.comment.isLiked ? 1 : -1
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('点赞操作失败:', error)
        this.$message.error('操作失败')
      }
    },

    handleAction(command) {
      if (command === 'delete') {
        this.deleteComment()
      }
    },

    deleteComment() {
      this.$confirm('确定要删除这条评论吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete', this.comment.id)
      })
    },

    formatTime(time) {
      if (!time) return ''
      
      const now = new Date()
      const commentTime = new Date(time)
      const diff = now - commentTime
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 1) return '刚刚'
      if (minutes < 60) return `${minutes}分钟前`
      if (hours < 24) return `${hours}小时前`
      if (days < 7) return `${days}天前`
      
      return commentTime.toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style scoped>
.comment-item {
  padding: 15px 0;
  border-bottom: 1px solid #f0f2f5;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.author-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.author-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.comment-time {
  color: #8c9eff;
  font-size: 12px;
}

.comment-content {
  margin-left: 42px;
  margin-bottom: 10px;
}

.comment-text {
  color: #2c3e50;
  line-height: 1.6;
  font-size: 14px;
  word-wrap: break-word;
}

.comment-text >>> .mention {
  color: #409eff;
  font-weight: 600;
}

.comment-text >>> a {
  color: #409eff;
  text-decoration: none;
}

.comment-text >>> a:hover {
  text-decoration: underline;
}

.comment-images {
  display: flex;
  gap: 8px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.comment-image {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  cursor: pointer;
}

.comment-footer {
  margin-left: 42px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-stats {
  display: flex;
  align-items: center;
  gap: 15px;
}

.like-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #8c9eff;
  font-size: 12px;
  cursor: pointer;
  transition: color 0.2s ease;
  padding: 4px 8px;
  border-radius: 4px;
}

.like-btn:hover {
  color: #409eff;
  background: #f0f9ff;
}

.like-btn.liked {
  color: #f56c6c;
}

.like-btn i {
  font-size: 14px;
}

.comment-operations {
  display: flex;
  gap: 10px;
}

.replies-section {
  margin-left: 42px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f2f5;
}

.replies-header {
  margin-bottom: 10px;
  font-size: 12px;
  color: #8c9eff;
}

.replies-list {
  background: #fafbfc;
  border-radius: 8px;
  padding: 10px;
}

.replies-list .comment-item {
  padding: 10px 0;
}

.replies-list .comment-content {
  margin-left: 32px;
}

.replies-list .comment-footer {
  margin-left: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-content,
  .comment-footer {
    margin-left: 0;
  }
  
  .replies-section {
    margin-left: 0;
  }
  
  .replies-list .comment-content,
  .replies-list .comment-footer {
    margin-left: 0;
  }
  
  .comment-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .comment-footer {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .comment-images {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  }
  
  .comment-image {
    width: 60px;
    height: 60px;
  }
}
</style>