<template>
  <div class="profile-container">
    <div class="profile-header">
      <div class="header-bg"></div>
      <div class="profile-info">
        <div class="avatar-section">
          <div class="avatar-wrapper">
            <img 
              :src="getAvatarUrl(userInfo.avatar)" 
              alt="用户头像" 
              class="user-avatar"
              @error="handleAvatarError"
            />
            <div class="avatar-overlay" @click="showAvatarUpload = true">
              <i class="el-icon-camera"></i>
              <span>更换头像</span>
            </div>
          </div>
        </div>
        <div class="user-details">
          <h2>{{ userInfo.realName || userInfo.username }}</h2>
          <p class="user-title">{{ userInfo.title || '校友' }}</p>
          <div class="user-stats">
            <div class="stat-item">
              <span class="stat-number">{{ userInfo.postCount || 0 }}</span>
              <span class="stat-label">发布</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ userInfo.favoriteCount || 0 }}</span>
              <span class="stat-label">收藏</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ userInfo.followCount || 0 }}</span>
              <span class="stat-label">关注</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="profile-content">
      <el-tabs v-model="activeTab" class="profile-tabs">
        <!-- 个人信息 -->
        <el-tab-pane label="个人信息" name="info">
          <div class="info-section">
            <el-card class="info-card">
              <div slot="header" class="card-header">
                <span>基本信息</span>
                <el-button 
                  type="text" 
                  @click="handleEditSave"
                  :icon="editMode ? 'el-icon-check' : 'el-icon-edit'"
                  :loading="saving"
                >
                  {{ editMode ? '保存' : '编辑' }}
                </el-button>
              </div>
              
              <el-form :model="userForm" label-width="100px" class="user-form">
                <el-form-item label="真实姓名">
                  <el-input 
                    v-model="userForm.realName" 
                    :disabled="!editMode"
                    placeholder="请输入真实姓名"
                  />
                </el-form-item>
                
                <el-form-item label="用户名">
                  <el-input 
                    v-model="userForm.username" 
                    disabled
                    placeholder="用户名不可修改"
                  />
                </el-form-item>
                
                <el-form-item label="邮箱">
                  <el-input 
                    v-model="userForm.email" 
                    :disabled="!editMode"
                    placeholder="请输入邮箱地址"
                  />
                </el-form-item>
                
                <el-form-item label="手机号">
                  <el-input 
                    v-model="userForm.phone" 
                    :disabled="!editMode"
                    placeholder="请输入手机号"
                  />
                </el-form-item>
                
                <el-form-item label="毕业年份">
                  <el-date-picker
                    v-model="userForm.graduationYear"
                    type="year"
                    :disabled="!editMode"
                    placeholder="选择毕业年份"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item label="专业">
                  <el-input 
                    v-model="userForm.major" 
                    :disabled="!editMode"
                    placeholder="请输入专业"
                  />
                </el-form-item>
                
                <el-form-item label="工作单位">
                  <el-input 
                    v-model="userForm.company" 
                    :disabled="!editMode"
                    placeholder="请输入工作单位"
                  />
                </el-form-item>
                
                <el-form-item label="职位">
                  <el-input 
                    v-model="userForm.position" 
                    :disabled="!editMode"
                    placeholder="请输入职位"
                  />
                </el-form-item>
                
                <el-form-item label="个人简介">
                  <el-input 
                    v-model="userForm.bio" 
                    type="textarea"
                    :rows="4"
                    :disabled="!editMode"
                    placeholder="请输入个人简介"
                  />
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 我的收藏 -->
        <el-tab-pane label="我的收藏" name="favorites">
          <div class="favorites-section">
            <div class="section-header">
              <h3>收藏的内容</h3>
              <el-button @click="refreshFavorites" icon="el-icon-refresh">刷新</el-button>
            </div>
            
            <div class="favorites-tabs">
              <el-tabs v-model="favoriteType" @tab-click="handleFavoriteTypeChange">
                <el-tab-pane label="新闻文章" name="news">
                  <div class="favorite-list">
                    <div 
                      v-for="item in favoriteNews" 
                      :key="item.id" 
                      class="favorite-item"
                    >
                      <div class="item-image">
                        <img :src="item.imageUrl || defaultNewsImage" alt="新闻图片" />
                      </div>
                      <div class="item-content">
                        <h4 @click="goToNews(item.id)">{{ item.title }}</h4>
                        <p class="item-summary">{{ item.summary }}</p>
                        <div class="item-meta">
                          <span class="meta-item">
                            <i class="el-icon-user"></i>
                            {{ item.author }}
                          </span>
                          <span class="meta-item">
                            <i class="el-icon-time"></i>
                            {{ formatDate(item.createTime) }}
                          </span>
                          <span class="meta-item">
                            <i class="el-icon-view"></i>
                            {{ item.viewCount }}
                          </span>
                        </div>
                      </div>
                      <div class="item-actions">
                        <el-button 
                          type="text" 
                          @click="removeFavorite(item.id, 'news')"
                          icon="el-icon-delete"
                        >
                          取消收藏
                        </el-button>
                      </div>
                    </div>
                    
                    <div v-if="favoriteNews.length === 0" class="empty-state">
                      <i class="el-icon-star-off"></i>
                      <p>暂无收藏的新闻</p>
                    </div>
                  </div>
                </el-tab-pane>
                
                <el-tab-pane label="活动" name="activities">
                  <div class="favorite-list">
                    <div 
                      v-for="item in favoriteActivities" 
                      :key="item.id" 
                      class="favorite-item"
                    >
                      <div class="item-image">
                        <img :src="item.imageUrl || defaultActivityImage" alt="活动图片" />
                      </div>
                      <div class="item-content">
                        <h4>{{ item.title }}</h4>
                        <p class="item-summary">{{ item.description }}</p>
                        <div class="item-meta">
                          <span class="meta-item">
                            <i class="el-icon-location"></i>
                            {{ item.location }}
                          </span>
                          <span class="meta-item">
                            <i class="el-icon-time"></i>
                            {{ formatDate(item.startTime) }}
                          </span>
                        </div>
                      </div>
                      <div class="item-actions">
                        <el-button 
                          type="text" 
                          @click="removeFavorite(item.id, 'activity')"
                          icon="el-icon-delete"
                        >
                          取消收藏
                        </el-button>
                      </div>
                    </div>
                    
                    <div v-if="favoriteActivities.length === 0" class="empty-state">
                      <i class="el-icon-star-off"></i>
                      <p>暂无收藏的活动</p>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </el-tab-pane>

        <!-- 账户设置 -->
        <el-tab-pane label="账户设置" name="settings">
          <div class="settings-section">
            <el-card class="settings-card">
              <div slot="header">
                <span>密码修改</span>
              </div>
              
              <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="120px">
                <el-form-item label="当前密码" prop="currentPassword">
                  <el-input 
                    v-model="passwordForm.currentPassword" 
                    type="password" 
                    placeholder="请输入当前密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item label="新密码" prop="newPassword">
                  <el-input 
                    v-model="passwordForm.newPassword" 
                    type="password" 
                    placeholder="请输入新密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item label="确认新密码" prop="confirmPassword">
                  <el-input 
                    v-model="passwordForm.confirmPassword" 
                    type="password" 
                    placeholder="请再次输入新密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="changePassword">修改密码</el-button>
                  <el-button @click="resetPasswordForm">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 头像上传对话框 -->
    <el-dialog title="更换头像" :visible.sync="showAvatarUpload" width="500px">
      <div class="avatar-upload-container">
        <div class="current-avatar">
          <img :src="getAvatarUrl(userInfo.avatar)" alt="当前头像" @error="handleAvatarError" />
          <p>当前头像</p>
        </div>
        
        <div class="upload-section">
          <el-upload
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            :http-request="handleAvatarUpload"
            :loading="avatarUploading"
          >
            <img v-if="newAvatarUrl" :src="newAvatarUrl" class="new-avatar">
            <div v-else class="upload-placeholder">
              <i class="el-icon-plus"></i>
              <div class="upload-text">点击选择新头像</div>
            </div>
          </el-upload>
          <p class="upload-tips">支持 JPG、PNG 格式，文件大小不超过 2MB</p>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelAvatarUpload">取消</el-button>
        <el-button 
          type="primary" 
          @click="saveAvatar" 
          :loading="avatarUploading"
          :disabled="!newAvatarUrl"
        >
          保存头像
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/utils/api'

export default {
  name: 'Profile',
  data() {
    return {
      activeTab: 'info',
      editMode: false,
      saving: false,
      userInfo: {},
      userForm: {
        realName: '',
        username: '',
        email: '',
        phone: '',
        graduationYear: null,
        major: '',
        company: '',
        position: '',
        bio: ''
      },
      // 收藏相关
      favoriteType: 'news',
      favoriteNews: [],
      favoriteActivities: [],
      // 头像上传
      showAvatarUpload: false,
      newAvatarUrl: '',
      avatarUploading: false,
      // 密码修改
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        currentPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' }
        ]
      },
      defaultAvatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGN0ZBIi8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iMzUiIHI9IjE1IiBmaWxsPSIjOEM5M0QiLz4KPHBhdGggZD0iTTI1IDc1QzI1IDY1IDM1IDU1IDUwIDU1QzY1IDU1IDc1IDY1IDc1IDc1IiBzdHJva2U9IiM4QzkzRCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo=',
      defaultNewsImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjVGN0ZBIi8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjE2MCIgaGVpZ2h0PSI4MCIgZmlsbD0iI0U1RTdFQiIvPgo8dGV4dCB4PSIxMDAiIHk9IjY1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOEM5M0QiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCI+5paw6Ze7PC90ZXh0Pgo8L3N2Zz4K',
      defaultActivityImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjVGN0ZBIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjYwIiByPSIzMCIgZmlsbD0iI0U1RTdFQiIvPgo8dGV4dCB4PSIxMDAiIHk9IjY1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOEM5M0QiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiI+5rS75YqoPC90ZXh0Pgo8L3N2Zz4K'
    }
  },
  computed: {
    user() {
      return this.$store.state.user
    }
  },
  mounted() {
    this.loadUserInfo()
    this.loadFavorites()
  },
  methods: {
    async loadUserInfo() {
      try {
        if (!this.user || !this.user.id) {
          this.$message.error('请先登录')
          this.$router.push('/login')
          return
        }
        
        const response = await api.get('/user/profile', {
          headers: {
            'User-Id': this.user.id
          }
        })
        if (response.code === 200) {
          this.userInfo = response.data
          this.userForm = { ...response.data }
          // 更新store中的用户信息
          this.$store.dispatch('setUser', { ...this.user, ...response.data })
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
        this.$message.error('加载用户信息失败')
      }
    },
    
    handleEditSave() {
      if (this.editMode) {
        this.updateProfile()
      } else {
        this.editMode = true
      }
    },
    
    async updateProfile() {
      try {
        this.saving = true
        if (!this.user || !this.user.id) {
          this.$message.error('请先登录')
          return
        }
        
        const response = await api.put('/user/profile', this.userForm, {
          headers: {
            'User-Id': this.user.id
          }
        })
        
        if (response.code === 200) {
          this.userInfo = { ...this.userInfo, ...this.userForm }
          // 更新Vuex中的用户信息
          this.$store.dispatch('setUser', { ...this.user, ...this.userForm })
          this.$message.success('个人资料更新成功')
          this.editMode = false
        } else {
          this.$message.error(response.message || '更新失败')
        }
      } catch (error) {
        console.error('更新个人资料失败:', error)
        this.$message.error('更新个人资料失败')
      } finally {
        this.saving = false
      }
    },
    
    async loadFavorites() {
      try {
        const headers = {
          'User-Id': (this.user && this.user.id) || '1' // 临时使用用户ID 1，实际应该从登录状态获取
        }
        
        const [newsResponse, activitiesResponse] = await Promise.all([
          api.get('/user/favorites/news', { headers }),
          api.get('/user/favorites/activities', { headers })
        ])
        
        if (newsResponse.code === 200) {
          this.favoriteNews = newsResponse.data
        }
        if (activitiesResponse.code === 200) {
          this.favoriteActivities = activitiesResponse.data
        }
      } catch (error) {
        console.error('加载收藏列表失败:', error)
      }
    },
    
    refreshFavorites() {
      this.loadFavorites()
    },
    
    handleFavoriteTypeChange() {
      // 切换收藏类型时的处理
    },
    
    async removeFavorite(itemId, type) {
      try {
        if (!this.user || !this.user.id) {
          this.$message.error('请先登录')
          return
        }
        
        const response = await api.delete(`/user/favorites/${type}/${itemId}`, {
          headers: {
            'User-Id': this.user.id
          }
        })
        if (response.code === 200) {
          this.$message.success('取消收藏成功')
          this.loadFavorites()
        }
      } catch (error) {
        console.error('取消收藏失败:', error)
        this.$message.error('取消收藏失败')
      }
    },
    
    goToNews(newsId) {
      this.$router.push(`/news/${newsId}`)
    },
    
    // 头像上传相关方法
    beforeAvatarUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    
    handleAvatarUpload(options) {
      const { file } = options
      this.avatarUploading = true
      
      const formData = new FormData()
      formData.append('file', file)
      
      // 调用真实的文件上传API
      api.post('/upload/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(response => {
        if (response.code === 200) {
          // 使用服务器返回的URL
          this.newAvatarUrl = `http://localhost:8080${response.data.url}`
          this.$message.success('头像上传成功')
        } else {
          this.$message.error(response.message || '头像上传失败')
        }
      }).catch(error => {
        console.error('头像上传失败:', error)
        this.$message.error('头像上传失败')
      }).finally(() => {
        this.avatarUploading = false
      })
    },
    
    async saveAvatar() {
      try {
        if (!this.user || !this.user.id) {
          this.$message.error('请先登录')
          return
        }
        
        const response = await api.put('/user/avatar', {
          avatar: this.newAvatarUrl
        }, {
          headers: {
            'User-Id': this.user.id
          }
        })
        
        if (response.code === 200) {
          this.userInfo.avatar = this.newAvatarUrl
          // 更新Vuex中的用户信息
          this.$store.dispatch('setUser', { ...this.user, avatar: this.newAvatarUrl })
          this.$message.success('头像更新成功')
          this.showAvatarUpload = false
          this.newAvatarUrl = ''
        } else {
          this.$message.error(response.message || '头像更新失败')
        }
      } catch (error) {
        console.error('头像更新失败:', error)
        this.$message.error('头像更新失败')
      }
    },
    
    cancelAvatarUpload() {
      this.showAvatarUpload = false
      this.newAvatarUrl = ''
    },
    
    // 密码修改
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    },
    
    changePassword() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (valid) {
          try {
            if (!this.user || !this.user.id) {
              this.$message.error('请先登录')
              return
            }
            
            const response = await api.put('/user/password', {
              currentPassword: this.passwordForm.currentPassword,
              newPassword: this.passwordForm.newPassword
            }, {
              headers: {
                'User-Id': this.user.id
              }
            })
            
            if (response.code === 200) {
              this.$message.success('密码修改成功')
              this.resetPasswordForm()
            } else {
              this.$message.error(response.message)
            }
          } catch (error) {
            console.error('密码修改失败:', error)
            this.$message.error('密码修改失败')
          }
        }
      })
    },
    
    resetPasswordForm() {
      this.passwordForm = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.$refs.passwordForm && this.$refs.passwordForm.resetFields()
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },
    
    getAvatarUrl(avatar) {
      if (!avatar) return this.defaultAvatar
      if (avatar.startsWith('http')) return avatar
      if (avatar.startsWith('/uploads') || avatar.startsWith('/images')) {
        return `http://localhost:8080${avatar}`
      }
      return avatar
    },
    
    handleAvatarError(event) {
      event.target.src = this.defaultAvatar
    }
  }
}
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.profile-header {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.profile-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40px;
  background: linear-gradient(transparent, rgba(0,0,0,0.3));
  color: white;
  display: flex;
  align-items: flex-end;
}

.avatar-section {
  margin-right: 30px;
}

.avatar-wrapper {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid white;
  box-shadow: 0 4px 20px rgba(0,0,0,0.2);
}

.user-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.avatar-overlay i {
  font-size: 24px;
  margin-bottom: 5px;
}

.avatar-overlay span {
  font-size: 12px;
}

.user-details h2 {
  margin: 0 0 10px 0;
  font-size: 32px;
  font-weight: 600;
}

.user-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  opacity: 0.9;
}

.user-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

.profile-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.profile-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  overflow: hidden;
}

.info-card, .settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-form {
  padding: 20px 0;
}

.favorites-section {
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
}

.favorite-list {
  min-height: 400px;
}

.favorite-item {
  display: flex;
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.favorite-item:hover {
  transform: translateY(-2px);
}

.item-image {
  width: 120px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 16px;
  flex-shrink: 0;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-content {
  flex: 1;
}

.item-content h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 16px;
  cursor: pointer;
  transition: color 0.2s;
}

.item-content h4:hover {
  color: #409eff;
}

.item-summary {
  margin: 0 0 12px 0;
  color: #64748b;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  color: #8c939d;
  font-size: 12px;
}

.meta-item i {
  margin-right: 4px;
}

.item-actions {
  display: flex;
  align-items: center;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #8c939d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.settings-section {
  padding: 20px;
}

.avatar-upload-container {
  display: flex;
  gap: 40px;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.current-avatar {
  text-align: center;
}

.current-avatar img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e2e8f0;
}

.current-avatar p {
  margin: 10px 0 0 0;
  color: #64748b;
  font-size: 14px;
}

.upload-section {
  text-align: center;
}

.avatar-uploader {
  width: 100px;
  height: 100px;
  border: 2px dashed #d9d9d9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
  overflow: hidden;
  margin-bottom: 10px;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.new-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
}

.upload-placeholder i {
  font-size: 24px;
  margin-bottom: 5px;
}

.upload-text {
  font-size: 12px;
}

.upload-tips {
  margin: 0;
  color: #8c939d;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px;
  }
  
  .avatar-section {
    margin-right: 0;
    margin-bottom: 20px;
  }
  
  .user-stats {
    justify-content: center;
  }
  
  .profile-content {
    padding: 20px 10px;
  }
  
  .favorite-item {
    flex-direction: column;
  }
  
  .item-image {
    width: 100%;
    height: 200px;
    margin-right: 0;
    margin-bottom: 16px;
  }
  
  .avatar-upload-container {
    flex-direction: column;
    gap: 20px;
  }
}
</style>