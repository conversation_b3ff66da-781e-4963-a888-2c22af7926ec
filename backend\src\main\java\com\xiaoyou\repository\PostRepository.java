package com.xiaoyou.repository;

import com.xiaoyou.entity.Post;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PostRepository extends JpaRepository<Post, Long> {
    
    // 获取已发布的帖子，按置顶和创建时间排序
    @Query(value = "SELECT p FROM Post p JOIN FETCH p.author WHERE p.status = 'PUBLISHED' ORDER BY p.isPinned DESC, p.createTime DESC",
           countQuery = "SELECT COUNT(p) FROM Post p WHERE p.status = 'PUBLISHED'")
    Page<Post> findPublishedPostsOrderByPinnedAndCreateTime(Pageable pageable);
    
    // 根据分类获取已发布的帖子
    @Query(value = "SELECT p FROM Post p JOIN FETCH p.author WHERE p.status = 'PUBLISHED' AND p.category = :category ORDER BY p.isPinned DESC, p.createTime DESC",
           countQuery = "SELECT COUNT(p) FROM Post p WHERE p.status = 'PUBLISHED' AND p.category = :category")
    Page<Post> findPublishedPostsByCategory(@Param("category") String category, Pageable pageable);
    
    // 搜索已发布的帖子（标题或内容包含关键词）
    @Query(value = "SELECT p FROM Post p JOIN FETCH p.author WHERE p.status = 'PUBLISHED' AND (p.title LIKE CONCAT('%', :keyword, '%') OR p.content LIKE CONCAT('%', :keyword, '%')) ORDER BY p.isPinned DESC, p.createTime DESC",
           countQuery = "SELECT COUNT(p) FROM Post p WHERE p.status = 'PUBLISHED' AND (p.title LIKE CONCAT('%', :keyword, '%') OR p.content LIKE CONCAT('%', :keyword, '%'))")
    Page<Post> searchPublishedPosts(@Param("keyword") String keyword, Pageable pageable);
    
    // 获取用户的帖子
    @Query(value = "SELECT p FROM Post p JOIN FETCH p.author WHERE p.author.id = :authorId ORDER BY p.createTime DESC",
           countQuery = "SELECT COUNT(p) FROM Post p WHERE p.author.id = :authorId")
    Page<Post> findPostsByAuthor(@Param("authorId") Long authorId, Pageable pageable);
    
    // 获取精华帖子
    @Query(value = "SELECT p FROM Post p JOIN FETCH p.author WHERE p.status = 'PUBLISHED' AND p.isFeatured = true ORDER BY p.createTime DESC",
           countQuery = "SELECT COUNT(p) FROM Post p WHERE p.status = 'PUBLISHED' AND p.isFeatured = true")
    Page<Post> findFeaturedPosts(Pageable pageable);
    
    // 获取置顶帖子
    @Query("SELECT p FROM Post p JOIN FETCH p.author WHERE p.status = 'PUBLISHED' AND p.isPinned = true ORDER BY p.createTime DESC")
    List<Post> findPinnedPosts();
    
    // 增加浏览量
    @Modifying
    @Query("UPDATE Post p SET p.viewCount = p.viewCount + 1 WHERE p.id = :id")
    void incrementViewCount(@Param("id") Long id);
    
    // 增加点赞数
    @Modifying
    @Query("UPDATE Post p SET p.likeCount = p.likeCount + 1 WHERE p.id = :id")
    void incrementLikeCount(@Param("id") Long id);
    
    // 减少点赞数
    @Modifying
    @Query("UPDATE Post p SET p.likeCount = p.likeCount - 1 WHERE p.id = :id AND p.likeCount > 0")
    void decrementLikeCount(@Param("id") Long id);
    
    // 增加评论数
    @Modifying
    @Query("UPDATE Post p SET p.commentCount = p.commentCount + 1 WHERE p.id = :id")
    void incrementCommentCount(@Param("id") Long id);
    
    // 减少评论数
    @Modifying
    @Query("UPDATE Post p SET p.commentCount = p.commentCount - 1 WHERE p.id = :id AND p.commentCount > 0")
    void decrementCommentCount(@Param("id") Long id);
    
    // 管理员获取所有帖子（包括草稿、隐藏等）
    @Query(value = "SELECT p FROM Post p JOIN FETCH p.author ORDER BY p.createTime DESC",
           countQuery = "SELECT COUNT(p) FROM Post p")
    Page<Post> findAllPostsForAdmin(Pageable pageable);
    
    // 根据状态获取帖子
    @Query(value = "SELECT p FROM Post p JOIN FETCH p.author WHERE p.status = :status ORDER BY p.createTime DESC",
           countQuery = "SELECT COUNT(p) FROM Post p WHERE p.status = :status")
    Page<Post> findPostsByStatus(@Param("status") String status, Pageable pageable);
    
    // 统计各种状态的帖子数量
    @Query("SELECT COUNT(p) FROM Post p WHERE p.status = :status")
    Long countPostsByStatus(@Param("status") String status);
    
    // 统计用户发帖数量
    @Query("SELECT COUNT(p) FROM Post p WHERE p.author.id = :authorId AND p.status = 'PUBLISHED'")
    Long countPublishedPostsByAuthor(@Param("authorId") Long authorId);
    
    // 统计今日发帖数量
    @Query("SELECT COUNT(p) FROM Post p WHERE DATE(p.createTime) = CURRENT_DATE")
    Long countTodayPosts();
}