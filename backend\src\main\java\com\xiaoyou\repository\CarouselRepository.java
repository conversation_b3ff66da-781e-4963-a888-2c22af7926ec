package com.xiaoyou.repository;

import com.xiaoyou.entity.Carousel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CarouselRepository extends JpaRepository<Carousel, Long> {
    
    /**
     * 查找所有启用的轮播图，按排序顺序排列
     */
    @Query("SELECT c FROM Carousel c WHERE c.isActive = true ORDER BY c.sortOrder ASC, c.id ASC")
    List<Carousel> findActiveCarouselsOrderBySortOrder();
    
    /**
     * 查找所有轮播图，按排序顺序排列
     */
    @Query("SELECT c FROM Carousel c ORDER BY c.sortOrder ASC, c.id ASC")
    List<Carousel> findAllOrderBySortOrder();
}