package com.xiaoyou.service;

import com.xiaoyou.dto.PostRequest;
import com.xiaoyou.entity.Post;
import com.xiaoyou.entity.User;
import com.xiaoyou.repository.PostRepository;
import com.xiaoyou.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class PostService {
    
    @Autowired
    private PostRepository postRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    // 获取已发布的帖子列表
    public Page<Post> getPublishedPosts(Pageable pageable) {
        return postRepository.findPublishedPostsOrderByPinnedAndCreateTime(pageable);
    }
    
    // 根据分类获取帖子
    public Page<Post> getPostsByCategory(String category, Pageable pageable) {
        return postRepository.findPublishedPostsByCategory(category, pageable);
    }
    
    // 搜索帖子
    public Page<Post> searchPosts(String keyword, Pageable pageable) {
        return postRepository.searchPublishedPosts(keyword, pageable);
    }
    
    // 获取精华帖子
    public Page<Post> getFeaturedPosts(Pageable pageable) {
        return postRepository.findFeaturedPosts(pageable);
    }
    
    // 获取置顶帖子
    public List<Post> getPinnedPosts() {
        return postRepository.findPinnedPosts();
    }
    
    // 增加浏览量
    @Transactional
    public void incrementViewCount(Long postId) {
        postRepository.incrementViewCount(postId);
    }
    
    // 根据ID获取帖子详情
    @Transactional
    public Post getPostById(Long id) {
        Post post = postRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("帖子不存在"));
        
        // 增加浏览量
        postRepository.incrementViewCount(id);
        
        return post;
    }
    
    // 创建帖子
    public Post createPost(PostRequest postRequest, Long authorId) {
        User author = userRepository.findById(authorId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        Post post = new Post();
        post.setTitle(postRequest.getTitle());
        post.setContent(postRequest.getContent());
        post.setSummary(postRequest.getSummary());
        
        // 处理图片 - 优先使用images数组的第一张图片，fallback到imageUrl
        if (postRequest.getImages() != null && !postRequest.getImages().isEmpty()) {
            post.setImageUrl(postRequest.getImages().get(0));
        } else if (postRequest.getImageUrl() != null) {
            post.setImageUrl(postRequest.getImageUrl());
        }
        
        post.setCategory(postRequest.getCategory() != null ? postRequest.getCategory() : "GENERAL");
        post.setStatus(postRequest.getStatus() != null ? postRequest.getStatus() : "PUBLISHED");
        post.setIsPinned(postRequest.getIsPinned() != null ? postRequest.getIsPinned() : false);
        post.setIsFeatured(postRequest.getIsFeatured() != null ? postRequest.getIsFeatured() : false);
        post.setAuthor(author);
        
        Post savedPost = postRepository.save(post);
        
        // 更新用户发帖数量 - 处理null值
        Integer currentPostCount = author.getPostCount();
        if (currentPostCount == null) {
            currentPostCount = 0;
        }
        author.setPostCount(currentPostCount + 1);
        userRepository.save(author);
        
        return savedPost;
    }
    
    // 更新帖子
    public Post updatePost(Long id, PostRequest postRequest, Long authorId) {
        Post post = postRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("帖子不存在"));
        
        // 检查权限：只有作者或管理员可以编辑
        User currentUser = userRepository.findById(authorId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        if (!post.getAuthor().getId().equals(authorId) && !currentUser.getRole().equals(User.Role.ADMIN)) {
            throw new RuntimeException("没有权限编辑此帖子");
        }
        
        post.setTitle(postRequest.getTitle());
        post.setContent(postRequest.getContent());
        post.setSummary(postRequest.getSummary());
        
        // 处理图片 - 优先使用images数组的第一张图片，fallback到imageUrl
        if (postRequest.getImages() != null && !postRequest.getImages().isEmpty()) {
            post.setImageUrl(postRequest.getImages().get(0));
        } else if (postRequest.getImageUrl() != null) {
            post.setImageUrl(postRequest.getImageUrl());
        }
        
        post.setCategory(postRequest.getCategory());
        post.setStatus(postRequest.getStatus());
        
        // 只有管理员可以设置置顶和精华
        if (currentUser.getRole().equals(User.Role.ADMIN)) {
            post.setIsPinned(postRequest.getIsPinned() != null ? postRequest.getIsPinned() : post.getIsPinned());
            post.setIsFeatured(postRequest.getIsFeatured() != null ? postRequest.getIsFeatured() : post.getIsFeatured());
        }
        
        return postRepository.save(post);
    }
    
    // 删除帖子
    @Transactional
    public void deletePost(Long id, Long userId) {
        Post post = postRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("帖子不存在"));
        
        // 检查权限：只有作者或管理员可以删除
        User currentUser = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
        
        if (!post.getAuthor().getId().equals(userId) && !currentUser.getRole().equals(User.Role.ADMIN)) {
            throw new RuntimeException("没有权限删除此帖子");
        }
        
        // 更新用户发帖数量
        User author = post.getAuthor();
        if (author.getPostCount() > 0) {
            author.setPostCount(author.getPostCount() - 1);
            userRepository.save(author);
        }
        
        postRepository.deleteById(id);
    }
    
    // 获取用户的帖子
    public Page<Post> getUserPosts(Long userId, Pageable pageable) {
        return postRepository.findPostsByAuthor(userId, pageable);
    }
    
    // 管理员获取所有帖子
    public Page<Post> getAllPostsForAdmin(Pageable pageable) {
        return postRepository.findAllPostsForAdmin(pageable);
    }
    
    // 根据状态获取帖子
    public Page<Post> getPostsByStatus(String status, Pageable pageable) {
        return postRepository.findPostsByStatus(status, pageable);
    }
    
    // 统计帖子数量
    public Long countPostsByStatus(String status) {
        return postRepository.countPostsByStatus(status);
    }
    
    // 切换帖子置顶状态
    @Transactional
    public Post togglePinStatus(Long id) {
        Post post = postRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("帖子不存在"));
        
        post.setIsPinned(!post.getIsPinned());
        return postRepository.save(post);
    }
    
    // 切换帖子精华状态
    @Transactional
    public Post toggleFeaturedStatus(Long id) {
        Post post = postRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("帖子不存在"));
        
        post.setIsFeatured(!post.getIsFeatured());
        return postRepository.save(post);
    }
    
    // 更改帖子状态
    @Transactional
    public Post updatePostStatus(Long id, String status) {
        Post post = postRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("帖子不存在"));
        
        post.setStatus(status);
        return postRepository.save(post);
    }
    
    // 统计方法
    public long getTotalPosts() {
        return postRepository.count();
    }
    
    public long getTotalComments() {
        // 返回所有帖子的评论总数
        return postRepository.findAll().stream()
                .mapToLong(post -> post.getCommentCount() != null ? post.getCommentCount() : 0)
                .sum();
    }
    
    public long getTotalLikes() {
        // 返回所有帖子的点赞总数
        return postRepository.findAll().stream()
                .mapToLong(post -> post.getLikeCount() != null ? post.getLikeCount() : 0)
                .sum();
    }
    
    public long getTodayPosts() {
        // 返回今天创建的帖子数量
        return postRepository.countTodayPosts();
    }
}