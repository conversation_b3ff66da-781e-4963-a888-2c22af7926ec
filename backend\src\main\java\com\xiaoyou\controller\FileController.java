package com.xiaoyou.controller;

import com.xiaoyou.dto.ApiResponse;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/upload")
@CrossOrigin(origins = {"http://localhost:8081", "http://localhost:3000"})
public class FileController {
    
    @PostMapping("/image")
    public ApiResponse<Map<String, String>> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            System.out.println("收到文件上传请求: " + file.getOriginalFilename());
            
            // 检查文件是否为空
            if (file.isEmpty()) {
                System.out.println("文件为空");
                return ApiResponse.error(400, "文件不能为空");
            }
            
            // 检查文件类型
            String contentType = file.getContentType();
            System.out.println("文件类型: " + contentType);
            if (contentType == null || !contentType.startsWith("image/")) {
                System.out.println("文件类型不正确");
                return ApiResponse.error(400, "只能上传图片文件");
            }
            
            // 检查文件大小 (2MB)
            System.out.println("文件大小: " + file.getSize() + " bytes");
            if (file.getSize() > 2 * 1024 * 1024) {
                System.out.println("文件过大");
                return ApiResponse.error(400, "文件大小不能超过2MB");
            }
            
            // 使用绝对路径创建上传目录
            String uploadPath = System.getProperty("user.dir") + "/uploads/images/";
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                boolean created = uploadDir.mkdirs();
                System.out.println("创建上传目录: " + uploadPath + ", 结果: " + created);
            }
            
            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String filename = UUID.randomUUID().toString() + extension;
            
            // 保存文件
            Path filePath = Paths.get(uploadPath + filename);
            System.out.println("保存文件到: " + filePath.toString());
            Files.copy(file.getInputStream(), filePath);
            
            // 验证文件是否保存成功
            File savedFile = new File(filePath.toString());
            if (!savedFile.exists()) {
                System.out.println("文件保存失败");
                return ApiResponse.error(500, "文件保存失败");
            }
            
            System.out.println("文件保存成功，大小: " + savedFile.length() + " bytes");
            
            // 返回文件访问URL
            String fileUrl = "http://localhost:8080/uploads/images/" + filename;
            Map<String, String> result = new HashMap<>();
            result.put("url", fileUrl);
            result.put("filename", filename);
            
            System.out.println("返回文件URL: " + fileUrl);
            return ApiResponse.success("文件上传成功", result);
            
        } catch (IOException e) {
            System.err.println("文件上传异常: " + e.getMessage());
            e.printStackTrace();
            return ApiResponse.error(500, "文件上传失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("未知异常: " + e.getMessage());
            e.printStackTrace();
            return ApiResponse.error(500, "系统错误: " + e.getMessage());
        }
    }
}