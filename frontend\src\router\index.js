import Vue from 'vue'
import VueRouter from 'vue-router'
import Login from '../views/Login.vue'
import Register from '../views/Register.vue'
import Home from '../views/Home.vue'
import NewsView from '../views/NewsView.vue'
import NewsDetail from '../views/NewsDetail.vue'
import ActivityView from '../views/ActivityView.vue'
import ActivityDetail from '../views/ActivityDetail.vue'
import JobView from '../views/JobView.vue'
import JobDetail from '../views/JobDetail.vue'
import AdminLogin from '../views/AdminLogin.vue'
import AdminDashboard from '../views/AdminDashboard.vue'
import Profile from '../views/Profile.vue'
import AlumniAssociation from '../views/AlumniAssociation.vue'
import AlumniAssociationDetail from '../views/AlumniAssociationDetail.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/register',
    name: 'Register',
    component: Register
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/news',
    name: 'NewsView',
    component: NewsView
  },
  {
    path: '/news/:id',
    name: 'NewsDetail',
    component: NewsDetail
  },
  {
    path: '/alumni-association',
    name: 'AlumniAssociation',
    component: AlumniAssociation
  },
  {
    path: '/alumni-association/:id',
    name: 'AlumniAssociationDetail',
    component: AlumniAssociationDetail
  },
  {
    path: '/activities',
    name: 'ActivityView',
    component: ActivityView
  },
  {
    path: '/activities/:id',
    name: 'ActivityDetail',
    component: ActivityDetail
  },
  {
    path: '/jobs',
    name: 'JobView',
    component: JobView
  },
  {
    path: '/jobs/:id',
    name: 'JobDetail',
    component: JobDetail
  },
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: AdminLogin
  },
  {
    path: '/admin/dashboard',
    name: 'AdminDashboard',
    component: AdminDashboard
  },
  {
    path: '/alumni-association/:id',
    name: 'AlumniAssociationDetail',
    component: () => import('@/views/AlumniAssociationDetail.vue')
  },
  {
    path: '/forum',
    name: 'Forum',
    component: () => import('@/views/Forum.vue')
  },
  {
    path: '/forum/posts/:id',
    name: 'PostDetail',
    component: () => import('@/views/PostDetail.vue')
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router
