package com.xiaoyou.service;

import com.xiaoyou.dto.NewsRequest;
import com.xiaoyou.entity.News;
import com.xiaoyou.repository.NewsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class NewsService {
    
    @Autowired
    private NewsRepository newsRepository;
    
    public Page<News> getAllPublishedNews(Pageable pageable) {
        return newsRepository.findByPublishedTrueOrderByPublishTimeDesc(pageable);
    }
    
    public Page<News> searchNews(String title, Pageable pageable) {
        if (title == null || title.trim().isEmpty()) {
            return getAllPublishedNews(pageable);
        }
        return newsRepository.findByTitleContainingIgnoreCaseAndPublishedTrueOrderByPublishTimeDesc(title, pageable);
    }
    
    @Transactional
    public News getNewsById(Long id) {
        News news = newsRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("新闻不存在"));
        newsRepository.incrementViewCount(id);
        return news;
    }
    
    public News createNews(NewsRequest newsRequest) {
        News news = new News();
        news.setTitle(newsRequest.getTitle());
        news.setContent(newsRequest.getContent());
        news.setSummary(newsRequest.getSummary());
        news.setAuthor(newsRequest.getAuthor());
        news.setImageUrl(newsRequest.getImageUrl());
        news.setPublished(newsRequest.getPublished());
        
        return newsRepository.save(news);
    }
    
    public News updateNews(Long id, NewsRequest newsRequest) {
        News news = newsRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("新闻不存在"));
        
        news.setTitle(newsRequest.getTitle());
        news.setContent(newsRequest.getContent());
        news.setSummary(newsRequest.getSummary());
        news.setAuthor(newsRequest.getAuthor());
        news.setImageUrl(newsRequest.getImageUrl());
        news.setPublished(newsRequest.getPublished());
        
        return newsRepository.save(news);
    }
    
    public void deleteNews(Long id) {
        if (!newsRepository.existsById(id)) {
            throw new RuntimeException("新闻不存在");
        }
        newsRepository.deleteById(id);
    }
    
    public Page<News> getAllNews(Pageable pageable) {
        return newsRepository.findAll(pageable);
    }
}