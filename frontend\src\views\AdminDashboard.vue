<template>
  <div class="admin-dashboard">
    <el-container>
      <!-- 头部 -->
      <el-header class="admin-header">
        <div class="header-content">
          <h1>管理员后台</h1>
          <div class="admin-info">
            <span>欢迎，{{ admin.realName || admin.username }}</span>
            <el-button type="text" style="color: white; margin-left: 20px;" @click="logout">
              退出登录
            </el-button>
          </div>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边栏 -->
        <el-aside width="200px" class="admin-sidebar">
          <el-menu
            :default-active="activeMenu"
            class="sidebar-menu"
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409eff"
            @select="handleMenuSelect"
          >
            <el-menu-item index="dashboard">
              <i class="el-icon-s-home"></i>
              <span>仪表板</span>
            </el-menu-item>
            <el-menu-item index="news">
              <i class="el-icon-document"></i>
              <span>新闻管理</span>
            </el-menu-item>
          <el-menu-item index="carousel">
            <i class="el-icon-picture"></i>
            <span slot="title">轮播图管理</span>
          </el-menu-item>
          <el-menu-item index="alumni-association">
            <i class="el-icon-office-building"></i>
            <span slot="title">校友会管理</span>
          </el-menu-item>
          <el-menu-item index="activities">
            <i class="el-icon-date"></i>
            <span slot="title">活动管理</span>
          </el-menu-item>
          <el-menu-item index="jobs">
            <i class="el-icon-suitcase"></i>
            <span slot="title">招聘信息管理</span>
          </el-menu-item>
          <el-menu-item index="forum">
            <i class="el-icon-chat-line-square"></i>
            <span slot="title">论坛管理</span>
          </el-menu-item>
            <el-menu-item index="users">
              <i class="el-icon-user"></i>
              <span>用户管理</span>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主要内容区域 -->
        <el-main class="admin-main">
          <!-- 仪表板概览 -->
          <div v-if="activeMenu === 'dashboard'" class="dashboard-content">
            <div class="page-header">
              <h2>系统概览</h2>
              <p>欢迎回到管理后台，这里是系统的整体数据概览</p>
            </div>
            
            <el-row :gutter="24" class="stats-row">
              <el-col :xs="24" :sm="12" :md="6">
                <div class="stat-card user-card">
                  <div class="stat-icon user-icon">
                    <i class="el-icon-user"></i>
                  </div>
                  <div class="stat-info">
                    <h3>{{ userCount }}</h3>
                    <p>注册用户</p>
                    <div class="stat-trend">
                      <span class="trend-up">
                        <i class="el-icon-top"></i>
                        +12%
                      </span>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="6">
                <div class="stat-card news-card">
                  <div class="stat-icon news-icon">
                    <i class="el-icon-document"></i>
                  </div>
                  <div class="stat-info">
                    <h3>{{ newsCount }}</h3>
                    <p>新闻文章</p>
                    <div class="stat-trend">
                      <span class="trend-up">
                        <i class="el-icon-top"></i>
                        +8%
                      </span>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="6">
                <div class="stat-card view-card">
                  <div class="stat-icon view-icon">
                    <i class="el-icon-view"></i>
                  </div>
                  <div class="stat-info">
                    <h3>{{ totalViews }}</h3>
                    <p>总阅读量</p>
                    <div class="stat-trend">
                      <span class="trend-up">
                        <i class="el-icon-top"></i>
                        +25%
                      </span>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="6">
                <div class="stat-card today-card">
                  <div class="stat-icon today-icon">
                    <i class="el-icon-date"></i>
                  </div>
                  <div class="stat-info">
                    <h3>{{ todayNews }}</h3>
                    <p>今日新闻</p>
                    <div class="stat-trend">
                      <span class="trend-up">
                        <i class="el-icon-top"></i>
                        +3
                      </span>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>

            <!-- 快捷操作区域 -->
            <div class="quick-actions">
              <h3>快捷操作</h3>
              <el-row :gutter="16">
                <el-col :span="8">
                  <div class="action-card" @click="handleMenuSelect('news')">
                    <i class="el-icon-edit-outline"></i>
                    <h4>发布新闻</h4>
                    <p>快速发布新的新闻资讯</p>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="action-card" @click="handleMenuSelect('carousel')">
                    <i class="el-icon-picture-outline"></i>
                    <h4>轮播图管理</h4>
                    <p>管理首页轮播图</p>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="action-card" @click="handleMenuSelect('users')">
                    <i class="el-icon-user-solid"></i>
                    <h4>用户管理</h4>
                    <p>管理系统用户信息</p>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 新闻管理 -->
          <div v-else-if="activeMenu === 'news'" class="news-management">
            <div class="page-header">
              <h2>新闻管理</h2>
              <p>管理系统中的所有新闻文章</p>
            </div>
            
            <div class="management-actions">
              <el-button type="primary" @click="showCreateNewsDialog">
                <i class="el-icon-plus"></i>
                添加新闻
              </el-button>
              <el-button @click="refreshNewsList">
                <i class="el-icon-refresh"></i>
                刷新
              </el-button>
            </div>

            <!-- 新闻列表 -->
            <div class="news-table-container">
              <el-table 
                :data="newsList" 
                v-loading="newsLoading"
                style="width: 100%"
                stripe
              >
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="title" label="标题" min-width="200" />
                <el-table-column prop="author" label="作者" width="120" />
                <el-table-column prop="published" label="状态" width="100">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.published ? 'success' : 'warning'">
                      {{ scope.row.published ? '已发布' : '草稿' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="viewCount" label="阅读量" width="100" />
                <el-table-column prop="createTime" label="创建时间" width="180">
                  <template slot-scope="scope">
                    {{ formatDateTime(scope.row.createTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template slot-scope="scope">
                    <el-button size="mini" @click="editNews(scope.row)">
                      编辑
                    </el-button>
                    <el-button 
                      size="mini" 
                      :type="scope.row.published ? 'warning' : 'success'"
                      @click="togglePublish(scope.row)"
                    >
                      {{ scope.row.published ? '下线' : '发布' }}
                    </el-button>
                    <el-button 
                      size="mini" 
                      type="danger" 
                      @click="deleteNews(scope.row)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页 -->
              <div class="pagination-wrapper">
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage"
                  :page-sizes="[10, 20, 50, 100]"
                  :page-size="pageSize"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="total"
                />
              </div>
            </div>
          </div>

          <!-- 轮播图管理 -->
          <div v-else-if="activeMenu === 'carousel'" class="carousel-management">
            <CarouselManagement />
          </div>
          
          <!-- 校友会管理 -->
          <!-- 校友会管理 -->
          <div v-else-if="activeMenu === 'alumni-association'" class="alumni-association-management">
            <AlumniAssociationManagement />
          </div>

          <!-- 活动管理 -->
          <div v-else-if="activeMenu === 'activities'" class="activity-management">
            <ActivityManagement />
          </div>

          <!-- 招聘信息管理 -->
          <div v-else-if="activeMenu === 'jobs'" class="job-management">
            <JobManagement />
          </div>

          <!-- 论坛管理 -->
          <div v-else-if="activeMenu === 'forum'" class="forum-management">
            <ForumManagement />
          </div>

          <!-- 用户管理 -->
          <div v-else-if="activeMenu === 'users'" class="user-management">
            <h2>用户管理</h2>
            <p>用户管理功能开发中...</p>
          </div>
        </el-main>
      </el-container>
    </el-container>

    <!-- 新闻编辑对话框 -->
    <el-dialog 
      :title="isEditMode ? '编辑新闻' : '添加新闻'"
      :visible.sync="newsDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form 
        :model="newsForm" 
        :rules="newsFormRules" 
        ref="newsForm" 
        label-width="80px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="newsForm.title" placeholder="请输入新闻标题" />
        </el-form-item>
        
        <el-form-item label="作者" prop="author">
          <el-input v-model="newsForm.author" placeholder="请输入作者姓名" />
        </el-form-item>
        
        <el-form-item label="摘要">
          <el-input 
            v-model="newsForm.summary" 
            type="textarea" 
            :rows="3"
            placeholder="请输入新闻摘要（可选）"
          />
        </el-form-item>
        
        <el-form-item label="封面图片">
          <div class="image-upload-container">
            <el-upload
              class="image-uploader"
              action="#"
              :show-file-list="false"
              :before-upload="beforeImageUpload"
              :http-request="handleImageUpload"
              :loading="uploadLoading"
            >
              <img v-if="newsForm.imageUrl" :src="newsForm.imageUrl" class="uploaded-image">
              <div v-else class="upload-placeholder">
                <i class="el-icon-plus"></i>
                <div class="upload-text">点击上传图片</div>
              </div>
            </el-upload>
            <div class="image-actions" v-if="newsForm.imageUrl">
              <el-button size="mini" @click="previewImage">预览</el-button>
              <el-button size="mini" type="danger" @click="removeImage">删除</el-button>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="内容" prop="content">
          <el-input 
            v-model="newsForm.content" 
            type="textarea" 
            :rows="10"
            placeholder="请输入新闻内容"
          />
        </el-form-item>
        
        <el-form-item label="发布状态">
          <el-switch
            v-model="newsForm.published"
            active-text="立即发布"
            inactive-text="保存草稿"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="newsDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveNews" :loading="uploadLoading">
          {{ isEditMode ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" :visible.sync="imagePreviewVisible" width="600px">
      <div class="image-preview-container">
        <img :src="newsForm.imageUrl" style="width: 100%; height: auto;" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import api from '@/utils/api'
import CarouselManagement from '@/views/admin/CarouselManagement.vue'
import AlumniAssociationManagement from '@/views/admin/AlumniAssociationManagement.vue'
import ActivityManagement from '@/views/admin/ActivityManagement.vue'
import JobManagement from '@/views/admin/JobManagement.vue'
import ForumManagement from '@/views/admin/ForumManagement.vue'

export default {
  name: 'AdminDashboard',
  components: {
    CarouselManagement,
    AlumniAssociationManagement,
    ActivityManagement,
    JobManagement,
    ForumManagement
  },
  data() {
    return {
      activeMenu: 'dashboard',
      userCount: 0,
      newsCount: 0,
      totalViews: 0,
      todayNews: 0,
      // 新闻管理相关数据
      newsList: [],
      newsLoading: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 新闻编辑对话框
      newsDialogVisible: false,
      newsForm: {
        id: null,
        title: '',
        content: '',
        summary: '',
        author: '',
        imageUrl: '',
        published: false
      },
      newsFormRules: {
        title: [
          { required: true, message: '请输入新闻标题', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入新闻内容', trigger: 'blur' }
        ],
        author: [
          { required: true, message: '请输入作者', trigger: 'blur' }
        ]
      },
      isEditMode: false,
      uploadLoading: false,
      imagePreviewVisible: false
    }
  },
  computed: {
    admin() {
      return this.$store.state.admin || {}
    }
  },
  mounted() {
    this.checkAdminAuth()
    this.loadDashboardData()
    // 清除可能存在的消息提示
    this.$nextTick(() => {
      // 确保页面加载完成后清除任何残留的消息
      if (this.$message) {
        this.$message.closeAll()
      }
    })
  },
  methods: {
    checkAdminAuth() {
      if (!this.admin || !this.admin.id) {
        this.$message.error('请先登录管理员账号')
        this.$router.push('/admin/login')
      }
    },
    async loadDashboardData() {
      try {
        // 这里可以添加获取统计数据的API调用
        // 暂时使用模拟数据
        this.userCount = 156
        this.newsCount = 23
        this.totalViews = 8520
        this.todayNews = 3
      } catch (error) {
        console.error('加载仪表板数据失败:', error)
      }
    },
    handleMenuSelect(index) {
      this.activeMenu = index
      if (index === 'news') {
        this.loadNewsList()
      }
    },
    
    // 新闻管理方法
    async loadNewsList() {
      this.newsLoading = true
      try {
        const response = await api.get('/news/admin/all', {
          params: {
            page: this.currentPage - 1,
            size: this.pageSize
          }
        })
        
        if (response.code === 200) {
          this.newsList = response.data.content
          this.total = response.data.totalElements
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('获取新闻列表失败:', error)
        this.$message.error('获取新闻列表失败')
      } finally {
        this.newsLoading = false
      }
    },
    
    refreshNewsList() {
      this.loadNewsList()
    },
    
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadNewsList()
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
      this.loadNewsList()
    },
    
    showCreateNewsDialog() {
      this.isEditMode = false
      this.newsForm = {
        id: null,
        title: '',
        content: '',
        summary: '',
        author: this.admin.realName || this.admin.username,
        imageUrl: '',
        published: false
      }
      this.newsDialogVisible = true
    },
    
    editNews(news) {
      this.isEditMode = true
      this.newsForm = {
        id: news.id,
        title: news.title,
        content: news.content,
        summary: news.summary,
        author: news.author,
        imageUrl: news.imageUrl,
        published: news.published
      }
      this.newsDialogVisible = true
    },
    
    async saveNews() {
      this.$refs.newsForm.validate(async (valid) => {
        if (valid) {
          try {
            let response
            if (this.isEditMode) {
              response = await api.put(`/news/${this.newsForm.id}`, this.newsForm)
            } else {
              response = await api.post('/news', this.newsForm)
            }
            
            if (response.code === 200) {
              this.$message.success(this.isEditMode ? '新闻更新成功' : '新闻创建成功')
              this.newsDialogVisible = false
              this.loadNewsList()
            } else {
              this.$message.error(response.message)
            }
          } catch (error) {
            console.error('保存新闻失败:', error)
            this.$message.error('保存新闻失败')
          }
        }
      })
    },
    
    async togglePublish(news) {
      try {
        const response = await api.put(`/news/${news.id}`, {
          ...news,
          published: !news.published
        })
        
        if (response.code === 200) {
          this.$message.success(news.published ? '新闻已下线' : '新闻已发布')
          this.loadNewsList()
        } else {
          this.$message.error(response.message)
        }
      } catch (error) {
        console.error('切换发布状态失败:', error)
        this.$message.error('操作失败')
      }
    },
    
    deleteNews(news) {
      this.$confirm(`确定要删除新闻"${news.title}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await api.delete(`/news/${news.id}`)
          
          if (response.code === 200) {
            this.$message.success('新闻删除成功')
            this.loadNewsList()
          } else {
            this.$message.error(response.message)
          }
        } catch (error) {
          console.error('删除新闻失败:', error)
          this.$message.error('删除新闻失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    
    formatDateTime(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    // 图片上传相关方法
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG/GIF 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      return true
    },
    
    handleImageUpload(options) {
      const { file } = options
      this.uploadLoading = true
      
      // 创建 FormData 对象
      const formData = new FormData()
      formData.append('file', file)
      
      // 上传到服务器
      api.post('/upload/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(response => {
        if (response.code === 200) {
          this.newsForm.imageUrl = `http://localhost:8080${response.data.url}`
          this.$message.success('图片上传成功')
        } else {
          this.$message.error(response.message || '图片上传失败')
        }
      }).catch(error => {
        console.error('图片上传失败:', error)
        this.$message.error('图片上传失败')
        
        // 如果服务器上传失败，回退到本地预览
        const reader = new FileReader()
        reader.onload = (e) => {
          this.newsForm.imageUrl = e.target.result
          this.$message.info('使用本地预览模式')
        }
        reader.readAsDataURL(file)
      }).finally(() => {
        this.uploadLoading = false
      })
    },
    
    previewImage() {
      this.imagePreviewVisible = true
    },
    
    removeImage() {
      this.newsForm.imageUrl = ''
      this.$message.success('图片已删除')
    },
    
    logout() {
      this.$store.dispatch('clearAdmin')
      this.$message.success('退出登录成功')
      this.$router.push('/admin/login')
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  height: 100vh;
  overflow: hidden;
}

.admin-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  line-height: 60px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.header-content h1 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
}

.admin-info {
  display: flex;
  align-items: center;
}

.admin-info span {
  margin-right: 15px;
  font-size: 14px;
}

.admin-sidebar {
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  box-shadow: 2px 0 8px rgba(0,0,0,0.1);
}

.sidebar-menu {
  border: none;
  height: calc(100vh - 60px);
  background: transparent;
}

.sidebar-menu .el-menu-item {
  height: 50px;
  line-height: 50px;
  margin: 5px 10px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sidebar-menu .el-menu-item:hover {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #409eff;
  color: white;
}

.sidebar-menu .el-menu-item i {
  margin-right: 8px;
  font-size: 16px;
}

.admin-main {
  background-color: #f5f7fa;
  padding: 24px;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.page-header {
  margin-bottom: 32px;
}

.page-header h2 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 700;
}

.page-header p {
  color: #64748b;
  font-size: 16px;
  margin: 0;
}

.dashboard-content h2,
.news-management h2,
.user-management h2 {
  color: #2c3e50;
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 600;
}

.stats-row {
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  border: 1px solid #e8eaec;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  position: relative;
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  background: inherit;
  opacity: 0.1;
}

.stat-icon i {
  font-size: 28px;
  color: white;
  z-index: 1;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.news-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.view-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.today-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-info h3 {
  font-size: 32px;
  color: #2c3e50;
  margin: 0 0 8px 0;
  font-weight: 700;
}

.stat-info p {
  color: #8c9eff;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.quick-actions {
  margin-top: 40px;
}

.quick-actions h3 {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
}

.action-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  border-color: #667eea;
}

.action-card i {
  font-size: 32px;
  color: #667eea;
  margin-bottom: 12px;
}

.action-card h4 {
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.action-card p {
  color: #64748b;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

.management-actions {
  margin-bottom: 24px;
}

.management-actions .el-button {
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 60px !important;
  }
  
  .sidebar-menu .el-menu-item span {
    display: none;
  }
  
  .admin-main {
    padding: 16px;
  }
  
  .stat-card {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }
  
  .stat-icon i {
    font-size: 20px;
  }
  
  .stat-info h3 {
    font-size: 24px;
  }
  
  .action-card {
    height: 120px;
    padding: 16px;
  }
  
  .action-card i {
    font-size: 24px;
  }
}

/* 图片上传样式 */
.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.image-uploader {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
  overflow: hidden;
}

.image-uploader:hover {
  border-color: #409eff;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
  height: 100%;
}

.upload-placeholder i {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
}

.image-actions {
  margin-top: 10px;
  display: flex;
  gap: 8px;
}

.news-table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 滚动条样式 */
.admin-main::-webkit-scrollbar {
  width: 6px;
}

.admin-main::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.admin-main::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.admin-main::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 新闻管理样式 */
.news-table-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
}

/* 图片上传样式 */
.image-upload-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.image-uploader {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  width: 200px;
  height: 200px;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s ease;
}

.image-uploader:hover {
  border-color: #409eff;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #8c939d;
}

.upload-placeholder i {
  font-size: 28px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.image-preview-container {
  text-align: center;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
}

.el-table td {
  border-bottom: 1px solid #f0f0f0;
}

.el-table .el-button--mini {
  padding: 5px 8px;
  font-size: 12px;
  border-radius: 4px;
}

/* 统计趋势样式 */
.stat-info p {
  color: #64748b;
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
}

.trend-up {
  color: #10b981;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 2px;
}

.trend-down {
  color: #ef4444;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 2px;
}
</style>
