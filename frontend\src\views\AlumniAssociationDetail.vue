<template>
  <Layout>
    <div class="association-detail-container">
      <div v-if="loading" class="loading-container">
        <el-loading-spinner></el-loading-spinner>
        <span>加载中...</span>
      </div>
      
      <div v-else-if="association" class="detail-content">
        <!-- 返回按钮 -->
        <div class="back-section">
          <el-button @click="goBack" icon="el-icon-arrow-left" type="text">
            返回校友会列表
          </el-button>
        </div>

        <!-- 校友会头部信息 -->
        <div class="association-header">
          <div class="header-left">
            <div class="logo-container">
              <img 
                v-if="association.logoUrl" 
                :src="getLogoUrl(association.logoUrl)" 
                :alt="association.name"
                class="association-logo"
                @error="handleLogoError"
              />
              <div v-else class="default-logo">
                <i class="el-icon-office-building"></i>
              </div>
            </div>
            <div class="header-info">
              <h1 class="association-name">{{ association.name }}</h1>
              <p class="establishment-info" v-if="association.establishmentDate">
                <i class="el-icon-time"></i>
                成立于 {{ formatDate(association.establishmentDate) }}
              </p>
              <div class="basic-stats">
                <span class="stat-item" v-if="association.memberCount">
                  <i class="el-icon-user"></i>
                  {{ association.memberCount }} 名成员
                </span>
                <span class="stat-item" v-if="association.president">
                  <i class="el-icon-user-solid"></i>
                  会长：{{ association.president }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="header-actions">
            <el-button type="primary" icon="el-icon-phone" @click="showContact">
              联系我们
            </el-button>
          </div>
        </div>

        <!-- 详细信息卡片 -->
        <el-row :gutter="20" class="detail-cards">
          <!-- 基本信息 -->
          <el-col :span="12" :xs="24">
            <div class="info-card">
              <h3 class="card-title">
                <i class="el-icon-info"></i>
                基本信息
              </h3>
              <div class="card-content">
                <div class="info-item" v-if="association.description">
                  <label>校友会简介：</label>
                  <p>{{ association.description }}</p>
                </div>
                <div class="info-item" v-if="association.address">
                  <label>地址：</label>
                  <p>{{ association.address }}</p>
                </div>
                <div class="info-item" v-if="association.establishmentDate">
                  <label>成立时间：</label>
                  <p>{{ formatFullDate(association.establishmentDate) }}</p>
                </div>
                <div class="info-item" v-if="association.memberCount">
                  <label>成员数量：</label>
                  <p>{{ association.memberCount }} 人</p>
                </div>
              </div>
            </div>
          </el-col>

          <!-- 联系信息 -->
          <el-col :span="12" :xs="24">
            <div class="info-card">
              <h3 class="card-title">
                <i class="el-icon-phone"></i>
                联系方式
              </h3>
              <div class="card-content">
                <div class="contact-item" v-if="association.president">
                  <i class="el-icon-user-solid"></i>
                  <div>
                    <label>会长</label>
                    <p>{{ association.president }}</p>
                  </div>
                </div>
                <div class="contact-item" v-if="association.phone">
                  <i class="el-icon-phone"></i>
                  <div>
                    <label>联系电话</label>
                    <p>{{ association.phone }}</p>
                  </div>
                </div>
                <div class="contact-item" v-if="association.email">
                  <i class="el-icon-message"></i>
                  <div>
                    <label>邮箱</label>
                    <p>{{ association.email }}</p>
                  </div>
                </div>
                <div class="contact-item" v-if="association.address">
                  <i class="el-icon-location"></i>
                  <div>
                    <label>地址</label>
                    <p>{{ association.address }}</p>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 活动介绍 -->
        <div class="info-card" v-if="association.activities">
          <h3 class="card-title">
            <i class="el-icon-star-on"></i>
            主要活动
          </h3>
          <div class="card-content">
            <div class="rich-content">
              {{ association.activities }}
            </div>
          </div>
        </div>

        <!-- 成就展示 -->
        <div class="info-card" v-if="association.achievements">
          <h3 class="card-title">
            <i class="el-icon-trophy"></i>
            主要成就
          </h3>
          <div class="card-content">
            <div class="rich-content">
              {{ association.achievements }}
            </div>
          </div>
        </div>
      </div>

      <div v-else class="error-container">
        <i class="el-icon-warning"></i>
        <p>校友会信息不存在或已被删除</p>
        <el-button type="primary" @click="goBack">返回列表</el-button>
      </div>

      <!-- 联系方式弹窗 -->
      <el-dialog
        title="联系方式"
        :visible.sync="contactDialogVisible"
        width="400px"
        center
      >
        <div class="contact-dialog">
          <div class="contact-item" v-if="association && association.president">
            <i class="el-icon-user-solid"></i>
            <div>
              <label>会长</label>
              <p>{{ association.president }}</p>
            </div>
          </div>
          <div class="contact-item" v-if="association && association.phone">
            <i class="el-icon-phone"></i>
            <div>
              <label>联系电话</label>
              <p>{{ association.phone }}</p>
            </div>
          </div>
          <div class="contact-item" v-if="association && association.email">
            <i class="el-icon-message"></i>
            <div>
              <label>邮箱</label>
              <p>{{ association.email }}</p>
            </div>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="contactDialogVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/Layout.vue'
import axios from 'axios'

export default {
  name: 'AlumniAssociationDetail',
  components: {
    Layout
  },
  data() {
    return {
      association: null,
      loading: false,
      contactDialogVisible: false
    }
  },
  mounted() {
    this.fetchAssociation()
  },
  methods: {
    async fetchAssociation() {
      try {
        this.loading = true
        const id = this.$route.params.id
        const response = await axios.get(`/api/alumni-associations/${id}`)
        
        if (response.data && response.data.code === 200) {
          this.association = response.data.data
        } else {
          this.$message.error('获取校友会详情失败')
        }
      } catch (error) {
        console.error('获取校友会详情失败:', error)
        this.$message.error('获取校友会详情失败')
      } finally {
        this.loading = false
      }
    },
    
    goBack() {
      this.$router.push('/alumni-association')
    },
    
    showContact() {
      this.contactDialogVisible = true
    },
    
    getLogoUrl(logoUrl) {
      if (!logoUrl) return ''
      if (logoUrl.startsWith('http')) return logoUrl
      if (logoUrl.startsWith('/uploads') || logoUrl.startsWith('/images')) {
        return `http://localhost:8080${logoUrl}`
      }
      return logoUrl
    },
    
    handleLogoError(event) {
      event.target.style.display = 'none'
      event.target.nextElementSibling.style.display = 'flex'
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.getFullYear() + '年'
    },
    
    formatFullDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
    }
  }
}
</script>

<style scoped>
.association-detail-container {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 20px;
}

.loading-container,
.error-container {
  text-align: center;
  padding: 80px 20px;
  color: #6b7280;
}

.loading-container span,
.error-container p {
  margin-top: 15px;
  font-size: 16px;
}

.error-container i {
  font-size: 48px;
  color: #f56565;
}

.back-section {
  margin-bottom: 20px;
}

.association-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left {
  display: flex;
  gap: 20px;
  flex: 1;
}

.logo-container {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.association-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12px;
}

.default-logo {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 32px;
}

.header-info {
  flex: 1;
}

.association-name {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 10px 0;
}

.establishment-info {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 5px;
}

.basic-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #4b5563;
  font-size: 14px;
}

.header-actions {
  flex-shrink: 0;
}

.detail-cards {
  margin-bottom: 20px;
}

.info-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f3f4f6;
}

.card-title i {
  color: #667eea;
}

.card-content {
  color: #4b5563;
}

.info-item {
  margin-bottom: 15px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  font-weight: 600;
  color: #374151;
  display: block;
  margin-bottom: 5px;
}

.info-item p {
  margin: 0;
  line-height: 1.6;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 15px;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-item i {
  width: 16px;
  color: #667eea;
  margin-top: 2px;
}

.contact-item div {
  flex: 1;
}

.contact-item label {
  font-weight: 600;
  color: #374151;
  display: block;
  margin-bottom: 3px;
  font-size: 13px;
}

.contact-item p {
  margin: 0;
  color: #4b5563;
  font-size: 14px;
}

.rich-content {
  line-height: 1.8;
  white-space: pre-wrap;
}

.contact-dialog .contact-item {
  padding: 15px 0;
  border-bottom: 1px solid #f3f4f6;
}

.contact-dialog .contact-item:last-child {
  border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .association-detail-container {
    padding: 10px;
  }
  
  .association-header {
    padding: 20px;
    flex-direction: column;
    gap: 20px;
  }
  
  .header-left {
    flex-direction: column;
    gap: 15px;
  }
  
  .logo-container {
    width: 60px;
    height: 60px;
    align-self: center;
  }
  
  .association-name {
    font-size: 22px;
    text-align: center;
  }
  
  .basic-stats {
    justify-content: center;
  }
  
  .header-actions {
    align-self: center;
  }
  
  .detail-cards .el-col {
    margin-bottom: 20px;
  }
  
  .info-card {
    padding: 15px;
  }
  
  .card-title {
    font-size: 16px;
  }
}
</style>
